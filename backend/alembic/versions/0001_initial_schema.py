"""Initial schema with documents and document_chunks tables

Revision ID: 0001
Revises: 
Create Date: 2024-01-01 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '0001'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Create initial schema with pgvector support."""
    
    # Enable pgvector extension
    op.execute("CREATE EXTENSION IF NOT EXISTS vector")
    
    # Create documents table
    op.create_table(
        'documents',
        sa.Column('id', sa.String(64), primary_key=True),
        sa.Column('title', sa.Text(), nullable=False),
        sa.Column('source', sa.Text(), nullable=True),
        sa.Column('metadata', postgresql.JSONB(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=True),
    )
    
    # Create document_chunks table
    op.create_table(
        'document_chunks',
        sa.Column('id', sa.String(64), primary_key=True),
        sa.Column('document_id', sa.String(64), sa.ForeignKey('documents.id', ondelete='CASCADE'), nullable=False),
        sa.Column('content', sa.Text(), nullable=False),
        sa.Column('metadata', postgresql.JSONB(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=True),
    )
    
    # Add embedding column using raw SQL (pgvector type)
    op.execute("ALTER TABLE document_chunks ADD COLUMN embedding VECTOR(768)")
    
    # Create indexes
    
    # Document chunks document_id index
    op.create_index(
        'idx_document_chunks_document_id',
        'document_chunks',
        ['document_id']
    )
    
    # Vector similarity index (IVFFlat for compatibility)
    op.execute("""
        CREATE INDEX idx_document_chunks_embedding 
        ON document_chunks USING ivfflat (embedding vector_cosine_ops)
    """)
    
    # Full-text search index
    op.execute("""
        CREATE INDEX idx_document_chunks_content_fts 
        ON document_chunks USING gin(to_tsvector('english', content))
    """)
    
    # Metadata indexes for filtering
    op.execute("""
        CREATE INDEX idx_documents_metadata 
        ON documents USING gin(metadata)
    """)
    
    op.execute("""
        CREATE INDEX idx_document_chunks_metadata 
        ON document_chunks USING gin(metadata)
    """)
    
    # Timestamp indexes for queries
    op.create_index(
        'idx_documents_created_at',
        'documents',
        ['created_at']
    )
    
    op.create_index(
        'idx_document_chunks_created_at',
        'document_chunks',
        ['created_at']
    )


def downgrade() -> None:
    """Drop all tables and indexes."""
    
    # Drop indexes first
    op.drop_index('idx_document_chunks_created_at')
    op.drop_index('idx_documents_created_at')
    op.execute("DROP INDEX IF EXISTS idx_document_chunks_metadata")
    op.execute("DROP INDEX IF EXISTS idx_documents_metadata")
    op.execute("DROP INDEX IF EXISTS idx_document_chunks_content_fts")
    op.execute("DROP INDEX IF EXISTS idx_document_chunks_embedding")
    op.drop_index('idx_document_chunks_document_id')
    
    # Drop tables
    op.drop_table('document_chunks')
    op.drop_table('documents')
    
    # Note: We don't drop the vector extension as it might be used by other applications
