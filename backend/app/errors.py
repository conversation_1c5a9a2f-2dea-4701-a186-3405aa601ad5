"""
Custom Exception Classes

This module defines custom exception classes for the BusinessLM application.
It provides structured error handling with proper error codes and messages.
"""

import logging
from typing import Optional, Dict, Any
from enum import Enum


class ErrorCode(Enum):
    """
    Error codes for different types of application errors.

    This enum provides standardized error codes that can be used
    for error tracking, logging, and API responses.
    """

    # Configuration Errors (1000-1099)
    CONFIG_MISSING = "CONFIG_MISSING"
    CONFIG_INVALID = "CONFIG_INVALID"
    CONFIG_API_KEY_MISSING = "CONFIG_API_KEY_MISSING"

    # Database Errors (1100-1199)
    DATABASE_CONNECTION_FAILED = "DATABASE_CONNECTION_FAILED"
    DATABASE_QUERY_FAILED = "DATABASE_QUERY_FAILED"
    DATABASE_MIGRATION_FAILED = "DATABASE_MIGRATION_FAILED"
    DATABASE_TRANSACTION_FAILED = "DATABASE_TRANSACTION_FAILED"

    # RAG/Embedding Errors (1200-1299)
    EMBEDDING_MODEL_LOAD_FAILED = "EMBEDDING_MODEL_LOAD_FAILED"
    EMBEDDING_GENERATION_FAILED = "EMBEDDING_GENERATION_FAILED"
    VECTOR_STORE_CONNECTION_FAILED = "VECTOR_STORE_CONNECTION_FAILED"
    VECTOR_SEARCH_FAILED = "VECTOR_SEARCH_FAILED"

    # Document Processing Errors (1300-1399)
    DOCUMENT_NOT_FOUND = "DOCUMENT_NOT_FOUND"
    DOCUMENT_LOAD_FAILED = "DOCUMENT_LOAD_FAILED"
    DOCUMENT_PARSE_FAILED = "DOCUMENT_PARSE_FAILED"
    DOCUMENT_CHUNK_FAILED = "DOCUMENT_CHUNK_FAILED"

    # LLM Errors (1400-1499)
    LLM_API_ERROR = "LLM_API_ERROR"
    LLM_TIMEOUT = "LLM_TIMEOUT"
    LLM_RATE_LIMIT = "LLM_RATE_LIMIT"
    LLM_INVALID_RESPONSE = "LLM_INVALID_RESPONSE"

    # Validation Errors (1500-1599)
    VALIDATION_ERROR = "VALIDATION_ERROR"
    INVALID_INPUT = "INVALID_INPUT"
    MISSING_REQUIRED_FIELD = "MISSING_REQUIRED_FIELD"

    # System Errors (1600-1699)
    TIMEOUT_ERROR = "TIMEOUT_ERROR"
    RESOURCE_NOT_AVAILABLE = "RESOURCE_NOT_AVAILABLE"
    PERMISSION_DENIED = "PERMISSION_DENIED"

    # Unknown/Generic Errors (1900-1999)
    UNKNOWN_ERROR = "UNKNOWN_ERROR"
    INTERNAL_ERROR = "INTERNAL_ERROR"


class BusinessLMError(Exception):
    """
    Base exception class for all BusinessLM application errors.

    This class provides a structured way to handle errors with
    error codes, context information, and proper logging.
    """

    def __init__(
        self,
        message: str,
        error_code: ErrorCode = ErrorCode.UNKNOWN_ERROR,
        context: Optional[Dict[str, Any]] = None,
        original_error: Optional[Exception] = None
    ):
        """
        Initialize BusinessLM error.

        Args:
            message: Human-readable error message
            error_code: Standardized error code
            context: Additional context information
            original_error: Original exception that caused this error
        """
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.context = context or {}
        self.original_error = original_error

        # Log the error
        logger = logging.getLogger(__name__)
        logger.error(
            f"BusinessLM Error [{error_code.value}]: {message}",
            extra={
                "error_code": error_code.value,
                "context": self.context,
                "original_error": str(original_error) if original_error else None
            }
        )

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert error to dictionary representation.

        Returns:
            Dictionary with error details
        """
        return {
            "error_code": self.error_code.value,
            "message": self.message,
            "context": self.context,
            "original_error": str(self.original_error) if self.original_error else None
        }

    def __str__(self) -> str:
        """String representation of the error."""
        return f"[{self.error_code.value}] {self.message}"


class ConfigurationError(BusinessLMError):
    """Exception raised for configuration-related errors."""

    def __init__(
        self,
        message: str,
        error_code: ErrorCode = ErrorCode.CONFIG_INVALID,
        context: Optional[Dict[str, Any]] = None,
        original_error: Optional[Exception] = None
    ):
        super().__init__(message, error_code, context, original_error)


class DatabaseError(BusinessLMError):
    """Exception raised for database-related errors."""

    def __init__(
        self,
        message: str,
        error_code: ErrorCode = ErrorCode.DATABASE_QUERY_FAILED,
        context: Optional[Dict[str, Any]] = None,
        original_error: Optional[Exception] = None
    ):
        super().__init__(message, error_code, context, original_error)


class EmbeddingError(BusinessLMError):
    """Exception raised for embedding/RAG-related errors."""

    def __init__(
        self,
        message: str,
        error_code: ErrorCode = ErrorCode.EMBEDDING_GENERATION_FAILED,
        context: Optional[Dict[str, Any]] = None,
        original_error: Optional[Exception] = None
    ):
        super().__init__(message, error_code, context, original_error)


class DocumentError(BusinessLMError):
    """Exception raised for document processing errors."""

    def __init__(
        self,
        message: str,
        error_code: ErrorCode = ErrorCode.DOCUMENT_LOAD_FAILED,
        context: Optional[Dict[str, Any]] = None,
        original_error: Optional[Exception] = None
    ):
        super().__init__(message, error_code, context, original_error)


class LLMError(BusinessLMError):
    """Exception raised for LLM-related errors."""

    def __init__(
        self,
        message: str,
        error_code: ErrorCode = ErrorCode.LLM_API_ERROR,
        context: Optional[Dict[str, Any]] = None,
        original_error: Optional[Exception] = None
    ):
        super().__init__(message, error_code, context, original_error)


class ValidationError(BusinessLMError):
    """Exception raised for validation errors."""

    def __init__(
        self,
        message: str,
        error_code: ErrorCode = ErrorCode.VALIDATION_ERROR,
        context: Optional[Dict[str, Any]] = None,
        original_error: Optional[Exception] = None
    ):
        super().__init__(message, error_code, context, original_error)


class TimeoutError(BusinessLMError):
    """Exception raised for timeout errors."""

    def __init__(
        self,
        message: str,
        error_code: ErrorCode = ErrorCode.TIMEOUT_ERROR,
        context: Optional[Dict[str, Any]] = None,
        original_error: Optional[Exception] = None
    ):
        super().__init__(message, error_code, context, original_error)


# Error handling utilities

def handle_database_error(func):
    """
    Decorator to handle database errors and convert them to DatabaseError.

    Args:
        func: Function to wrap

    Returns:
        Wrapped function
    """
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            if isinstance(e, BusinessLMError):
                raise
            raise DatabaseError(
                f"Database operation failed: {str(e)}",
                context={"function": func.__name__, "args": str(args), "kwargs": str(kwargs)},
                original_error=e
            )
    return wrapper


def handle_embedding_error(func):
    """
    Decorator to handle embedding errors and convert them to EmbeddingError.

    Args:
        func: Function to wrap

    Returns:
        Wrapped function
    """
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            if isinstance(e, BusinessLMError):
                raise
            raise EmbeddingError(
                f"Embedding operation failed: {str(e)}",
                context={"function": func.__name__, "args": str(args), "kwargs": str(kwargs)},
                original_error=e
            )
    return wrapper


def handle_llm_error(func):
    """
    Decorator to handle LLM errors and convert them to LLMError.

    Args:
        func: Function to wrap

    Returns:
        Wrapped function
    """
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            if isinstance(e, BusinessLMError):
                raise
            raise LLMError(
                f"LLM operation failed: {str(e)}",
                context={"function": func.__name__, "args": str(args), "kwargs": str(kwargs)},
                original_error=e
            )
    return wrapper


def log_and_reraise(logger: logging.Logger, error_message: str):
    """
    Utility function to log an error and re-raise it.

    Args:
        logger: Logger instance
        error_message: Error message to log
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                logger.error(f"{error_message}: {str(e)}")
                raise
        return wrapper
    return decorator
