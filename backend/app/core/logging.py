"""
Centralized Logging Configuration

This module provides centralized logging configuration for the application.
It sets up structured logging with proper formatters and handlers.
"""

import logging
import logging.config
import sys
import json
import traceback
from datetime import datetime
from typing import Optional, Dict, Any
from pathlib import Path

from ..config import get_settings


class EnhancedFormatter(logging.Formatter):
    """
    Enhanced formatter for human-readable logging with colors and better structure.
    """

    # Color codes for different log levels
    COLORS = {
        'DEBUG': '\033[36m',    # Cyan
        'INFO': '\033[32m',     # Green
        'WARNING': '\033[33m',  # Yellow
        'ERROR': '\033[31m',    # Red
        'CRITICAL': '\033[35m', # Magenta
    }
    RESET = '\033[0m'

    def __init__(self, use_colors: bool = True):
        """
        Initialize enhanced formatter.

        Args:
            use_colors: Whether to use colors in output
        """
        super().__init__()
        self.use_colors = use_colors

    def format(self, record: logging.LogRecord) -> str:
        """
        Format log record with enhanced structure.

        Args:
            record: Log record to format

        Returns:
            Formatted log message
        """
        # Create timestamp
        timestamp = datetime.fromtimestamp(record.created).strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]

        # Get color for level
        level_color = self.COLORS.get(record.levelname, '') if self.use_colors else ''
        reset_color = self.RESET if self.use_colors else ''

        # Format basic message
        formatted = f"{timestamp} | {level_color}{record.levelname:8}{reset_color} | {record.name:20} | {record.getMessage()}"

        # Add exception info if present
        if record.exc_info:
            formatted += f"\n{self.formatException(record.exc_info)}"

        # Add extra context if present
        if hasattr(record, 'context') and record.context:
            formatted += f" | Context: {record.context}"

        return formatted


class StructuredJSONFormatter(logging.Formatter):
    """
    JSON formatter for structured logging in production environments.
    """

    def format(self, record: logging.LogRecord) -> str:
        """
        Format log record as JSON.

        Args:
            record: Log record to format

        Returns:
            JSON formatted log message
        """
        log_entry = {
            "timestamp": datetime.fromtimestamp(record.created).isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
            "thread": record.thread,
            "process": record.process,
        }

        # Add exception info if present
        if record.exc_info:
            log_entry["exception"] = {
                "type": record.exc_info[0].__name__,
                "message": str(record.exc_info[1]),
                "traceback": traceback.format_exception(*record.exc_info)
            }

        # Add extra context if present
        if hasattr(record, 'context') and record.context:
            log_entry["context"] = record.context

        # Add error code if present (for BusinessLM errors)
        if hasattr(record, 'error_code'):
            log_entry["error_code"] = record.error_code

        return json.dumps(log_entry, ensure_ascii=False)


def setup_logging(
    log_level: Optional[str] = None,
    log_file: Optional[str] = None,
    enable_json_logging: bool = False,
    force_reconfigure: bool = False
) -> None:
    """
    Set up centralized logging configuration.

    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: Optional log file path
        enable_json_logging: Whether to use JSON formatting (useful for production)
        force_reconfigure: Whether to force reconfiguration even if already configured
    """
    global _logging_configured

    # Skip if already configured unless forced
    if _logging_configured and not force_reconfigure:
        return

    settings = get_settings()

    # Use provided log level or fall back to settings
    level = log_level or settings.LOG_LEVEL

    # Create logs directory if logging to file
    if log_file:
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)

    # Configure logging format
    if enable_json_logging:
        # JSON format for structured logging (production)
        formatter = StructuredJSONFormatter()
    else:
        # Enhanced human-readable format (development)
        formatter = EnhancedFormatter(use_colors=sys.stdout.isatty())

    # Configure handlers
    handlers = []

    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    handlers.append(console_handler)

    # File handler (if specified)
    if log_file:
        file_handler = logging.FileHandler(log_file)
        file_handler.setFormatter(formatter)
        handlers.append(file_handler)

    # Configure root logger
    logging.basicConfig(
        level=getattr(logging, level),
        handlers=handlers,
        force=True  # Override any existing configuration
    )

    # Set specific logger levels for noisy libraries
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING)

    # Set the configuration flag
    _logging_configured = True

    # Log the configuration
    logger = logging.getLogger(__name__)
    logger.info(f"Logging configured with level: {level}")
    if log_file:
        logger.info(f"Logging to file: {log_file}")


def get_logger(name: str) -> logging.Logger:
    """
    Get a logger instance with the specified name.

    Args:
        name: Logger name (typically __name__)

    Returns:
        Configured logger instance
    """
    return logging.getLogger(name)


# Global flag to track if logging has been configured
_logging_configured = False


def _setup_default_logging():
    """Set up default logging if not already configured."""
    global _logging_configured
    if not _logging_configured and not logging.getLogger().handlers:
        setup_logging()


# Initialize default logging
_setup_default_logging()


# Additional logging utilities

class LogContext:
    """
    Context manager for adding structured context to log messages.

    This allows adding consistent context information to all log messages
    within a specific scope.
    """

    def __init__(self, logger: logging.Logger, **context):
        """
        Initialize log context.

        Args:
            logger: Logger instance to add context to
            **context: Context key-value pairs
        """
        self.logger = logger
        self.context = context
        self.old_factory = None

    def __enter__(self):
        """Enter context manager."""
        self.old_factory = logging.getLogRecordFactory()

        def record_factory(*args, **kwargs):
            record = self.old_factory(*args, **kwargs)
            record.context = self.context
            return record

        logging.setLogRecordFactory(record_factory)
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Exit context manager."""
        logging.setLogRecordFactory(self.old_factory)


def log_function_call(logger: logging.Logger, log_args: bool = False, log_result: bool = False):
    """
    Decorator to log function calls with optional arguments and results.

    Args:
        logger: Logger instance to use
        log_args: Whether to log function arguments
        log_result: Whether to log function result

    Returns:
        Decorator function
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            func_name = f"{func.__module__}.{func.__name__}"

            # Log function entry
            if log_args:
                logger.debug(f"Calling {func_name} with args={args}, kwargs={kwargs}")
            else:
                logger.debug(f"Calling {func_name}")

            try:
                result = func(*args, **kwargs)

                # Log function success
                if log_result:
                    logger.debug(f"{func_name} completed successfully with result: {result}")
                else:
                    logger.debug(f"{func_name} completed successfully")

                return result

            except Exception as e:
                logger.error(f"{func_name} failed with error: {str(e)}")
                raise

        return wrapper
    return decorator


def log_performance(logger: logging.Logger, threshold_seconds: float = 1.0):
    """
    Decorator to log function performance if execution time exceeds threshold.

    Args:
        logger: Logger instance to use
        threshold_seconds: Threshold in seconds to trigger performance logging

    Returns:
        Decorator function
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            import time

            start_time = time.time()
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time

            if execution_time > threshold_seconds:
                logger.warning(
                    f"Performance warning: {func.__module__}.{func.__name__} "
                    f"took {execution_time:.2f}s (threshold: {threshold_seconds}s)"
                )

            return result

        return wrapper
    return decorator


def get_structured_logger(name: str, **default_context) -> logging.Logger:
    """
    Get a logger with default structured context.

    Args:
        name: Logger name
        **default_context: Default context to include in all log messages

    Returns:
        Logger instance with structured context
    """
    logger = logging.getLogger(name)

    # Add default context to all log records
    if default_context:
        old_factory = logging.getLogRecordFactory()

        def record_factory(*args, **kwargs):
            record = old_factory(*args, **kwargs)
            if not hasattr(record, 'context'):
                record.context = {}
            record.context.update(default_context)
            return record

        logging.setLogRecordFactory(record_factory)

    return logger
