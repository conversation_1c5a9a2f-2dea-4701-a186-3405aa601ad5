"""
Core Application Components

This package contains core application components including database
connections, logging configuration, and other foundational utilities.
"""

# Import core components for easy access
from .logging import (
    setup_logging,
    get_logger,
    get_structured_logger,
    LogContext,
    log_function_call,
    log_performance,
    EnhancedFormatter,
    StructuredJSONFormatter
)
from .db.database import get_db_context, init_db, close_db

__all__ = [
    # Logging
    "setup_logging",
    "get_logger",
    "get_structured_logger",
    "LogContext",
    "log_function_call",
    "log_performance",
    "EnhancedFormatter",
    "StructuredJSONFormatter",

    # Database
    "get_db_context",
    "init_db",
    "close_db",
]
