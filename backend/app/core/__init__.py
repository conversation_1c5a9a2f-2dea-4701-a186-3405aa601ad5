"""
Core Application Components

This package contains core application components including database
connections, logging configuration, and other foundational utilities.
"""

# Import core components for easy access
from .logging import setup_logging, get_logger
from .db.database import get_db_context, init_db, close_db

__all__ = [
    "setup_logging",
    "get_logger", 
    "get_db_context",
    "init_db",
    "close_db",
]
