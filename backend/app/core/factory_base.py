"""
Base Factory Pattern

This module provides a base factory pattern that can be shared across
different factory implementations to eliminate code duplication.
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, Type, TypeVar, Generic, List, Optional, Any, Callable

logger = logging.getLogger(__name__)

# Type variable for the product type that factories create
T = TypeVar('T')


class BaseFactory(Generic[T], ABC):
    """
    Abstract base factory class with common fallback and error handling logic.
    
    This class provides a standardized factory pattern that can be used
    across different types of factories (LLM adapters, embedding models, etc.)
    with consistent fallback behavior and error handling.
    """
    
    def __init__(self):
        """Initialize the factory with an empty registry."""
        self._registry: Dict[str, Type[T]] = {}
        self._availability_checkers: Dict[str, Callable[[], bool]] = {}
        self._fallback_order: List[str] = []
        self._initialized = False
    
    @abstractmethod
    def _populate_registry(self) -> None:
        """
        Populate the factory registry with available providers.
        
        This method must be implemented by concrete factory classes
        to register their specific providers and availability checkers.
        """
        pass
    
    def register_provider(
        self, 
        name: str, 
        provider_class: Type[T], 
        availability_checker: Optional[Callable[[], bool]] = None
    ) -> None:
        """
        Register a provider with the factory.
        
        Args:
            name: Provider name (e.g., 'openai', 'anthropic')
            provider_class: Class that implements the provider
            availability_checker: Function to check if provider is available
        """
        self._registry[name] = provider_class
        
        if availability_checker:
            self._availability_checkers[name] = availability_checker
        else:
            # Default availability checker (always available)
            self._availability_checkers[name] = lambda: True
    
    def set_fallback_order(self, providers: List[str]) -> None:
        """
        Set the fallback order for providers.
        
        Args:
            providers: List of provider names in fallback order
        """
        self._fallback_order = providers
    
    def _ensure_initialized(self) -> None:
        """Ensure the factory is initialized."""
        if not self._initialized:
            self._populate_registry()
            self._initialized = True
    
    def get_available_providers(self) -> List[str]:
        """
        Get list of available providers.
        
        Returns:
            List of provider names that are currently available
        """
        self._ensure_initialized()
        
        available = []
        for name, checker in self._availability_checkers.items():
            try:
                if checker():
                    available.append(name)
            except Exception as e:
                logger.debug(f"Availability check failed for {name}: {e}")
        
        return available
    
    def is_provider_available(self, provider: str) -> bool:
        """
        Check if a specific provider is available.
        
        Args:
            provider: Provider name to check
            
        Returns:
            True if provider is available, False otherwise
        """
        self._ensure_initialized()
        
        if provider not in self._availability_checkers:
            return False
        
        try:
            return self._availability_checkers[provider]()
        except Exception as e:
            logger.debug(f"Availability check failed for {provider}: {e}")
            return False
    
    def create(
        self, 
        provider: str, 
        fallback: bool = True, 
        **kwargs
    ) -> T:
        """
        Create an instance of the specified provider with fallback support.
        
        Args:
            provider: Provider name to create
            fallback: Whether to try fallback providers if the requested one fails
            **kwargs: Arguments to pass to the provider constructor
            
        Returns:
            Instance of the requested provider or a fallback
            
        Raises:
            ValueError: If provider is unknown and fallback is disabled
            ImportError: If provider dependencies are missing and fallback is disabled
        """
        self._ensure_initialized()
        
        # Try to create the requested provider
        try:
            if provider in self._registry:
                if self.is_provider_available(provider):
                    return self._registry[provider](**kwargs)
                else:
                    raise ImportError(f"Provider '{provider}' is not available")
            else:
                if fallback:
                    logger.warning(f"Unknown provider '{provider}', trying fallbacks")
                else:
                    raise ValueError(f"Unknown provider: {provider}")
        except Exception as e:
            if not fallback:
                raise
            
            logger.warning(f"Provider '{provider}' failed: {str(e)}. Trying fallbacks.")
        
        # Try fallback providers if enabled
        if fallback:
            return self._try_fallbacks(provider, **kwargs)
        
        # This should not be reached, but just in case
        raise RuntimeError(f"Failed to create provider '{provider}' and fallback is disabled")
    
    def _try_fallbacks(self, failed_provider: str, **kwargs) -> T:
        """
        Try fallback providers in order.
        
        Args:
            failed_provider: Name of the provider that failed
            **kwargs: Arguments to pass to provider constructors
            
        Returns:
            Instance of a working fallback provider
            
        Raises:
            RuntimeError: If all fallback providers fail
        """
        fallback_errors = []
        
        # Try providers in fallback order
        for provider_name in self._fallback_order:
            # Skip the provider that already failed
            if provider_name == failed_provider:
                continue
            
            # Skip if provider is not available
            if not self.is_provider_available(provider_name):
                continue
            
            try:
                logger.info(f"Falling back to {provider_name}")
                return self._registry[provider_name](**kwargs)
            except Exception as e:
                fallback_errors.append(f"{provider_name}: {str(e)}")
        
        # Try any remaining available providers not in fallback order
        available_providers = self.get_available_providers()
        for provider_name in available_providers:
            if provider_name == failed_provider or provider_name in self._fallback_order:
                continue
            
            try:
                logger.info(f"Falling back to {provider_name}")
                return self._registry[provider_name](**kwargs)
            except Exception as e:
                fallback_errors.append(f"{provider_name}: {str(e)}")
        
        # All fallbacks failed
        error_msg = f"All fallback providers failed: {'; '.join(fallback_errors)}"
        logger.error(error_msg)
        raise RuntimeError(error_msg)
    
    def get_registry(self) -> Dict[str, Type[T]]:
        """
        Get the provider registry.
        
        Returns:
            Dictionary mapping provider names to their classes
        """
        self._ensure_initialized()
        return self._registry.copy()
    
    def list_providers(self) -> List[str]:
        """
        List all registered provider names.
        
        Returns:
            List of all registered provider names
        """
        self._ensure_initialized()
        return list(self._registry.keys())


class FactoryMixin:
    """
    Mixin class that provides factory functionality to existing classes.
    
    This can be used to add factory capabilities to existing classes
    without requiring them to inherit from BaseFactory.
    """
    
    @classmethod
    def create_factory(cls, factory_class: Type[BaseFactory[T]]) -> BaseFactory[T]:
        """
        Create a factory instance for this class.
        
        Args:
            factory_class: Factory class to instantiate
            
        Returns:
            Factory instance
        """
        return factory_class()
    
    @classmethod
    def register_with_factory(
        cls, 
        factory: BaseFactory[T], 
        name: str, 
        availability_checker: Optional[Callable[[], bool]] = None
    ) -> None:
        """
        Register this class with a factory.
        
        Args:
            factory: Factory to register with
            name: Provider name
            availability_checker: Function to check availability
        """
        factory.register_provider(name, cls, availability_checker)


def create_simple_factory(
    providers: Dict[str, Type[T]], 
    availability_checkers: Optional[Dict[str, Callable[[], bool]]] = None,
    fallback_order: Optional[List[str]] = None
) -> BaseFactory[T]:
    """
    Create a simple factory with the given providers.
    
    This is a convenience function for creating factories without
    needing to subclass BaseFactory.
    
    Args:
        providers: Dictionary mapping provider names to classes
        availability_checkers: Optional availability checkers for providers
        fallback_order: Optional fallback order for providers
        
    Returns:
        Configured factory instance
    """
    class SimpleFactory(BaseFactory[T]):
        def _populate_registry(self) -> None:
            for name, provider_class in providers.items():
                checker = None
                if availability_checkers and name in availability_checkers:
                    checker = availability_checkers[name]
                self.register_provider(name, provider_class, checker)
            
            if fallback_order:
                self.set_fallback_order(fallback_order)
    
    return SimpleFactory()
