"""
Database Models

This module defines SQLAlchemy models for the application database.
"""

import uuid
from datetime import datetime
from typing import Dict, Any, Optional, List

from sqlalchemy import Column, String, Text, DateTime, ForeignKey, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

# Create base class for all models
Base = declarative_base()


class Document(Base):
    """
    Document model for storing document metadata.

    This model stores high-level document information including title,
    source, and metadata. The actual document content is stored in
    chunks in the DocumentChunk model.
    """
    __tablename__ = "documents"

    id = Column(String(64), primary_key=True, default=lambda: str(uuid.uuid4()))
    title = Column(Text, nullable=False)
    source = Column(Text, nullable=True)
    doc_metadata = Column(JSON, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationship to document chunks
    chunks = relationship("DocumentChunk", back_populates="document", cascade="all, delete-orphan")

    def __repr__(self) -> str:
        return f"<Document(id='{self.id}', title='{self.title[:50]}...')>"

    def to_dict(self) -> Dict[str, Any]:
        """Convert document to dictionary representation."""
        return {
            "id": self.id,
            "title": self.title,
            "source": self.source,
            "metadata": self.doc_metadata or {},
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "chunk_count": len(self.chunks) if self.chunks else 0
        }


class DocumentChunk(Base):
    """
    Document chunk model for storing document content with embeddings.

    This model stores individual chunks of document content along with
    their vector embeddings for similarity search.
    """
    __tablename__ = "document_chunks"

    id = Column(String(64), primary_key=True, default=lambda: str(uuid.uuid4()))
    document_id = Column(String(64), ForeignKey("documents.id", ondelete="CASCADE"), nullable=False)
    content = Column(Text, nullable=False)
    chunk_metadata = Column(JSON, nullable=True)
    # Note: embedding column is created via raw SQL in database.py due to vector type
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationship to parent document
    document = relationship("Document", back_populates="chunks")

    def __repr__(self) -> str:
        return f"<DocumentChunk(id='{self.id}', content='{self.content[:50]}...')>"

    def to_dict(self, include_embedding: bool = False) -> Dict[str, Any]:
        """
        Convert document chunk to dictionary representation.

        Args:
            include_embedding: Whether to include the embedding vector

        Returns:
            Dictionary representation of the chunk
        """
        result = {
            "id": self.id,
            "document_id": self.document_id,
            "content": self.content,
            "metadata": self.chunk_metadata or {},
            "created_at": self.created_at.isoformat() if self.created_at else None
        }

        # Note: embedding would need to be fetched separately via raw SQL
        # due to the vector type not being directly supported by SQLAlchemy

        return result


# Utility functions for working with models

def create_document(
    title: str,
    source: Optional[str] = None,
    metadata: Optional[Dict[str, Any]] = None
) -> Document:
    """
    Create a new document instance.

    Args:
        title: Document title
        source: Document source (file path, URL, etc.)
        metadata: Additional metadata

    Returns:
        Document instance
    """
    return Document(
        title=title,
        source=source,
        doc_metadata=metadata or {}
    )


def create_document_chunk(
    document_id: str,
    content: str,
    metadata: Optional[Dict[str, Any]] = None
) -> DocumentChunk:
    """
    Create a new document chunk instance.

    Args:
        document_id: ID of the parent document
        content: Chunk content
        metadata: Additional metadata

    Returns:
        DocumentChunk instance
    """
    return DocumentChunk(
        document_id=document_id,
        content=content,
        chunk_metadata=metadata or {}
    )
