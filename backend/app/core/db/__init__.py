"""
Database Package

This package contains database-related components including connection
management, models, and utilities.
"""

from .database import (
    get_db_context,
    init_db,
    close_db,
    create_tables,
    drop_tables,
    reset_database,
    get_table_info,
    check_database_connection,
    ensure_pgvector_extension,
    get_engine
)
from .models import Document, DocumentChunk, create_document, create_document_chunk
from .migrations import (
    run_migrations,
    create_migration,
    rollback_migration,
    get_migration_status,
    initialize_migrations,
    get_current_revision,
    get_available_revisions
)

__all__ = [
    # Database connection and management
    "get_db_context",
    "init_db",
    "close_db",
    "get_engine",
    "check_database_connection",

    # Table management
    "create_tables",
    "drop_tables",
    "reset_database",
    "get_table_info",

    # Extensions
    "ensure_pgvector_extension",

    # Migrations
    "run_migrations",
    "create_migration",
    "rollback_migration",
    "get_migration_status",
    "initialize_migrations",
    "get_current_revision",
    "get_available_revisions",

    # Models
    "Document",
    "DocumentChunk",
    "create_document",
    "create_document_chunk",
]
