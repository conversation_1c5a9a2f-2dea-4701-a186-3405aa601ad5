"""
Database Connection Management

This module provides database connection management using SQLAlchemy
with connection pooling and context management.
"""

import logging
from contextlib import contextmanager
from typing import Generator, Optional

import sqlalchemy as sa
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import QueuePool

from ...config import get_settings

logger = logging.getLogger(__name__)

# Global database engine and session factory
_engine: Optional[sa.Engine] = None
_session_factory: Optional[sessionmaker] = None


def init_db() -> None:
    """
    Initialize the database connection and session factory.

    This function creates the SQLAlchemy engine with connection pooling
    and sets up the session factory for database operations.
    """
    global _engine, _session_factory

    settings = get_settings()

    logger.info("Initializing database connection")

    # Create engine with connection pooling
    _engine = create_engine(
        settings.DATABASE_URL,
        poolclass=QueuePool,
        pool_size=settings.DATABASE_POOL_SIZE,
        max_overflow=settings.DATABASE_MAX_OVERFLOW,
        pool_timeout=settings.DATABASE_POOL_TIMEOUT,
        pool_recycle=settings.DATABASE_POOL_RECYCLE,
        echo=settings.DEBUG,  # Log SQL queries in debug mode
        future=True  # Use SQLAlchemy 2.0 style
    )

    # Create session factory
    _session_factory = sessionmaker(
        bind=_engine,
        autocommit=False,
        autoflush=False,
        future=True
    )

    # Test the connection
    try:
        with _engine.connect() as conn:
            conn.execute(text("SELECT 1"))
        logger.info("Database connection established successfully")
    except Exception as e:
        logger.error(f"Failed to connect to database: {e}")
        raise


def close_db() -> None:
    """
    Close the database connection and clean up resources.
    """
    global _engine, _session_factory

    if _engine:
        logger.info("Closing database connection")
        _engine.dispose()
        _engine = None
        _session_factory = None


@contextmanager
def get_db_context() -> Generator[Session, None, None]:
    """
    Get a database session context manager.

    This context manager provides a database session that automatically
    handles transactions and cleanup. It commits on success and rolls back
    on exceptions.

    Yields:
        SQLAlchemy session instance

    Example:
        with get_db_context() as db:
            result = db.execute(text("SELECT * FROM documents"))
            rows = result.fetchall()
    """
    if _session_factory is None:
        init_db()

    session = _session_factory()
    try:
        yield session
        session.commit()
    except Exception as e:
        session.rollback()
        logger.error(f"Database transaction failed: {e}")
        raise
    finally:
        session.close()


def get_engine() -> sa.Engine:
    """
    Get the SQLAlchemy engine instance.

    Returns:
        SQLAlchemy engine

    Raises:
        RuntimeError: If database is not initialized
    """
    if _engine is None:
        raise RuntimeError("Database not initialized. Call init_db() first.")
    return _engine


def check_database_connection() -> bool:
    """
    Check if the database connection is working.

    Returns:
        True if connection is working, False otherwise
    """
    try:
        if _engine is None:
            init_db()

        with _engine.connect() as conn:
            conn.execute(text("SELECT 1"))
        return True
    except Exception as e:
        logger.error(f"Database connection check failed: {e}")
        return False


def create_tables() -> None:
    """
    Create database tables if they don't exist.

    This function creates the necessary tables for the RAG system
    including documents and document_chunks tables with vector support.
    """
    if _engine is None:
        init_db()

    logger.info("Creating database tables")

    with get_db_context() as db:
        # Enable pgvector extension
        db.execute(text("CREATE EXTENSION IF NOT EXISTS vector"))

        # Create documents table
        db.execute(text("""
            CREATE TABLE IF NOT EXISTS documents (
                id VARCHAR(64) PRIMARY KEY,
                title TEXT NOT NULL,
                source TEXT,
                doc_metadata JSONB,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
        """))

        # Create document_chunks table with vector support
        db.execute(text("""
            CREATE TABLE IF NOT EXISTS document_chunks (
                id VARCHAR(64) PRIMARY KEY,
                document_id VARCHAR(64) REFERENCES documents(id) ON DELETE CASCADE,
                content TEXT NOT NULL,
                chunk_metadata JSONB,
                embedding VECTOR(768),
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
        """))

        # Create indexes for better performance
        logger.info("Creating database indexes")

        # Document chunks document_id index
        db.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_document_chunks_document_id
            ON document_chunks(document_id)
        """))

        # Vector similarity index (IVFFlat for compatibility)
        db.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_document_chunks_embedding
            ON document_chunks USING ivfflat (embedding vector_cosine_ops)
        """))

        # Full-text search index
        db.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_document_chunks_content_fts
            ON document_chunks USING gin(to_tsvector('english', content))
        """))

        # Metadata indexes for filtering
        db.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_documents_metadata
            ON documents USING gin(metadata)
        """))

        db.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_document_chunks_metadata
            ON document_chunks USING gin(metadata)
        """))

        # Timestamp indexes for queries
        db.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_documents_created_at
            ON documents(created_at)
        """))

        db.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_document_chunks_created_at
            ON document_chunks(created_at)
        """))

    logger.info("Database tables created successfully")


def drop_tables(confirm: bool = False) -> None:
    """
    Drop all database tables.

    Args:
        confirm: Must be True to actually drop tables (safety measure)
    """
    if not confirm:
        raise ValueError("Must set confirm=True to drop tables")

    if _engine is None:
        init_db()

    logger.warning("Dropping all database tables")

    with get_db_context() as db:
        # Drop tables in reverse order due to foreign key constraints
        db.execute(text("DROP TABLE IF EXISTS document_chunks CASCADE"))
        db.execute(text("DROP TABLE IF EXISTS documents CASCADE"))

    logger.warning("Database tables dropped")


def reset_database(confirm: bool = False) -> None:
    """
    Reset the database by dropping and recreating all tables.

    Args:
        confirm: Must be True to actually reset database (safety measure)
    """
    if not confirm:
        raise ValueError("Must set confirm=True to reset database")

    logger.warning("Resetting database")
    drop_tables(confirm=True)
    create_tables()
    logger.info("Database reset completed")


def get_table_info() -> dict:
    """
    Get information about database tables.

    Returns:
        Dictionary with table information
    """
    if _engine is None:
        init_db()

    info = {}

    with get_db_context() as db:
        # Get table counts
        result = db.execute(text("SELECT COUNT(*) FROM documents"))
        info["documents_count"] = result.scalar()

        result = db.execute(text("SELECT COUNT(*) FROM document_chunks"))
        info["document_chunks_count"] = result.scalar()

        # Get table sizes (PostgreSQL specific)
        result = db.execute(text("""
            SELECT
                schemaname,
                tablename,
                pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
            FROM pg_tables
            WHERE tablename IN ('documents', 'document_chunks')
            ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
        """))

        info["table_sizes"] = [dict(row) for row in result.fetchall()]

        # Check if pgvector extension is installed
        result = db.execute(text("""
            SELECT EXISTS(
                SELECT 1 FROM pg_extension WHERE extname = 'vector'
            )
        """))
        info["pgvector_installed"] = result.scalar()

    return info


def ensure_pgvector_extension() -> None:
    """
    Ensure pgvector extension is installed.

    Raises:
        RuntimeError: If pgvector extension cannot be installed
    """
    if _engine is None:
        init_db()

    with get_db_context() as db:
        try:
            # Try to create the extension
            db.execute(text("CREATE EXTENSION IF NOT EXISTS vector"))
            logger.info("pgvector extension is available")
        except Exception as e:
            logger.error(f"Failed to create pgvector extension: {e}")
            raise RuntimeError(
                "pgvector extension is not available. "
                "Please install pgvector on your PostgreSQL server."
            )
