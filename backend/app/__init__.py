"""
BusinessLM Backend Application

This package contains the core backend application components including
configuration, database connections, and core utilities.
"""

__version__ = "0.1.0"

# Import core components for easy access
from .config import settings, get_settings
from .errors import (
    BusinessLMError,
    ConfigurationError,
    DatabaseError,
    EmbeddingError,
    DocumentError,
    LLMError,
    ValidationError,
    TimeoutError,
    ErrorCode,
    handle_database_error,
    handle_embedding_error,
    handle_llm_error,
    log_and_reraise
)

__all__ = [
    # Configuration
    "settings",
    "get_settings",

    # Error handling
    "BusinessLMError",
    "ConfigurationError",
    "DatabaseError",
    "EmbeddingError",
    "DocumentError",
    "LLMError",
    "ValidationError",
    "TimeoutError",
    "ErrorCode",
    "handle_database_error",
    "handle_embedding_error",
    "handle_llm_error",
    "log_and_reraise",
]
