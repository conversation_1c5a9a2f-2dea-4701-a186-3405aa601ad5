"""
LLM Adapter Package

This package provides a unified interface for interacting with different LLM providers.

Required dependencies:
    - For OpenAI: pip install openai
    - For Anthropic: pip install anthropic
    - For Google Gemini: pip install google-generativeai
    - For token counting: pip install tiktoken
    - For retry logic: pip install tenacity

Example usage:
    >>> adapter = get_llm_adapter("openai")
    >>> response = await adapter.chat([{"role": "user", "content": "Hello!"}])
    >>> print(response)  # response is a string

Streaming example:
    >>> adapter = get_llm_adapter("openai")
    >>> # When stream=True, the response is an async generator
    >>> async for chunk in adapter.chat([{"role": "user", "content": "Hello!"}], stream=True):
    >>>     print(chunk, end="", flush=True)
"""

# Import from base.py module
from .base import (
    ChatMessage,
    LLMAdapter,
    LLMConfig,
    log_llm_metrics,
)

# Import from adapter modules
from .openai import (
    OpenAIAdapter,
    OPENAI_AVAILABLE,
    TIKTOKEN_AVAILABLE,
)

from .anthropic import (
    AnthropicAdapter,
    ANTHROPIC_AVAILABLE,
)

from .gemini import (
    GeminiAdapter,
    GEMINI_AVAILABLE,
)

from .mock import MockAdapter

# Import from factory module
from .factory import (
    get_llm_adapter,
    get_llm_adapter_factory,
    register_adapter,
    ADAPTER_REGISTRY,
)

# Import tenacity availability flag
from .openai import TENACITY_AVAILABLE

# Group SDK flags for easier environment debug/validation
LLM_DEPENDENCIES = {
    "openai": OPENAI_AVAILABLE,
    "anthropic": ANTHROPIC_AVAILABLE,
    "gemini": GEMINI_AVAILABLE,
    "tiktoken": TIKTOKEN_AVAILABLE,
    "tenacity": TENACITY_AVAILABLE
}

# Define the list of exported symbols
__all__ = [
    "LLMAdapter",
    "OpenAIAdapter",
    "AnthropicAdapter",
    "GeminiAdapter",
    "MockAdapter",
    "get_llm_adapter",
    "get_llm_adapter_factory",
    "register_adapter",
    "ChatMessage",
    "LLMConfig",
    "OPENAI_AVAILABLE",
    "ANTHROPIC_AVAILABLE",
    "GEMINI_AVAILABLE",
    "TIKTOKEN_AVAILABLE",
    "TENACITY_AVAILABLE",
    "LLM_DEPENDENCIES",
    "ADAPTER_REGISTRY",
    "log_llm_metrics",
]
