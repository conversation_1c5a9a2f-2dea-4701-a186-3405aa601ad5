"""
LLM Adapter Factory

This module provides factory functions for creating LLM adapters with fallback support.
"""

import logging
from typing import Callable, Dict, Literal, Type

from ..app.core.factory_base import BaseFactory
from .base import LLMAdapter
from .openai import OpenAIAdapter, OPENAI_AVAILABLE
from .anthropic import Anthropic<PERSON>dapter, ANTHROPIC_AVAILABLE
from .gemini import <PERSON>Adapter, GEMINI_AVAILABLE
from .mock import MockAdapter

logger = logging.getLogger(__name__)


class LLMAdapterFactory(BaseFactory[LLMAdapter]):
    """Factory for creating LLM adapters with fallback support."""

    def _populate_registry(self) -> None:
        """Populate the LLM adapter registry."""
        # Register adapters with availability checks
        self.register_provider("openai", OpenAIAdapter, lambda: OPENAI_AVAILABLE)
        self.register_provider("anthropic", AnthropicAdapter, lambda: ANTHROPIC_AVAILABLE)
        self.register_provider("gem<PERSON>", GeminiAdapter, lambda: GEMINI_AVAILABLE)
        self.register_provider("mock", <PERSON><PERSON><PERSON>dapter, lambda: True)

        # Set fallback order
        self.set_fallback_order(["openai", "anthropic", "gemini", "mock"])


# Global factory instance
_llm_factory = LLMAdapterFactory()

# Legacy registry for backward compatibility
ADAPTER_REGISTRY: Dict[str, Type[LLMAdapter]] = {}


def register_adapter(name: str, adapter_class: Type[LLMAdapter]) -> None:
    """
    Register an LLM adapter with the registry.

    Args:
        name: The name of the adapter (e.g., 'openai', 'anthropic')
        adapter_class: The adapter class to register
    """
    ADAPTER_REGISTRY[name] = adapter_class


def get_llm_adapter(
    provider: Literal["openai", "anthropic", "gemini", "mock"] = "openai",
    fallback: bool = True,
    **kwargs
) -> LLMAdapter:
    """
    Factory function to get an LLM adapter.

    This function creates an adapter for the specified provider, with fallback logic
    if the requested provider is not available. The fallback order is:
    1. The requested provider
    2. OpenAI (if not already requested)
    3. Anthropic (if not already requested)
    4. Gemini (if not already requested)
    5. Mock adapter (as a last resort)

    Args:
        provider: The LLM provider to use ('openai', 'anthropic', 'gemini', or 'mock')
        fallback: Whether to fall back to other providers if the requested one is unavailable
        **kwargs: Additional arguments to pass to the adapter constructor, including:
            - api_key: API key for the provider
            - model: Model to use (e.g., 'gpt-4.1-2025-04-14', 'claude-3-7-sonnet-20250219',
                   'gemini-2.0-flash', 'gemini-2.5-pro-exp-03-25')
            - temperature: Controls randomness (0.0 = deterministic, 1.0 = creative)
            - max_tokens: Maximum number of tokens to generate
            - base_url: Base URL for API requests (for Azure OpenAI or proxies)
            - api_version: API version (required for Azure OpenAI)
            - organization: OpenAI organization ID

    Returns:
        An instance of LLMAdapter (OpenAIAdapter, AnthropicAdapter, GeminiAdapter, or MockAdapter)

    Raises:
        ValueError: If the provider is unknown or unavailable and fallback is False
        ImportError: If the required dependencies are not installed and fallback is False

    Example:
        >>> adapter = get_llm_adapter("openai", api_key="your-api-key", model="gpt-4")
        >>> response = await adapter.chat([{"role": "user", "content": "Hello!"}])
    """
    # Check for development mode - only use mock if explicitly requested or in development mode with mock provider
    import os
    if os.getenv("DEVELOPMENT_MODE", "false").lower() == "true" and provider == "mock":
        logger.info("Running in development mode with mock adapter")
        return MockAdapter(**kwargs)

    # Use the new factory for cleaner implementation
    return _llm_factory.create(provider, fallback=fallback, **kwargs)


def get_llm_adapter_factory() -> Dict[str, Type[LLMAdapter]]:
    """
    Get a dictionary of available LLM adapter factory functions.

    This can be used for dynamic loading of adapters via configuration or CLI tools.
    The returned dictionary maps provider names to their adapter classes, which can be
    instantiated with appropriate parameters.

    Example:
        >>> factory = get_llm_adapter_factory()
        >>> adapter_class = factory["openai"]
        >>> adapter = adapter_class(api_key="your-api-key")

    Returns:
        A dictionary mapping provider names ("openai", "anthropic", "gemini", "mock") to their
        respective adapter classes (OpenAIAdapter, AnthropicAdapter, GeminiAdapter, MockAdapter).
    """
    # Use the new factory to get the registry
    registry = _llm_factory.get_registry()

    # Update legacy registry for backward compatibility
    ADAPTER_REGISTRY.clear()
    ADAPTER_REGISTRY.update(registry)

    # Return a copy of the registry to prevent modification
    return registry.copy()
