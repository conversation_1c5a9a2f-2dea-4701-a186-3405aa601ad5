"""
LLM Adapter Factory

This module provides factory functions for creating LLM adapters.
"""

import logging
from typing import Callable, Dict, Literal, Type

from .base import LLMAdapter
from .openai import OpenAIAdapter, OPENAI_AVAILABLE
from .anthropic import AnthropicAdapter, ANTHROPIC_AVAILABLE
from .gemini import <PERSON>Adapter, GEMINI_AVAILABLE
from .mock import MockAdapter

logger = logging.getLogger(__name__)

# Create a registry for adapters
ADAPTER_REGISTRY: Dict[str, Type[LLMAdapter]] = {}


def register_adapter(name: str, adapter_class: Type[LLMAdapter]) -> None:
    """
    Register an LLM adapter with the registry.

    Args:
        name: The name of the adapter (e.g., 'openai', 'anthropic')
        adapter_class: The adapter class to register
    """
    ADAPTER_REGISTRY[name] = adapter_class


def get_llm_adapter(
    provider: Literal["openai", "anthropic", "gemini", "mock"] = "openai",
    fallback: bool = True,
    **kwargs
) -> LLMAdapter:
    """
    Factory function to get an LLM adapter.

    This function creates an adapter for the specified provider, with fallback logic
    if the requested provider is not available. The fallback order is:
    1. The requested provider
    2. OpenAI (if not already requested)
    3. Anthropic (if not already requested)
    4. Gemini (if not already requested)
    5. Mock adapter (as a last resort)

    Args:
        provider: The LLM provider to use ('openai', 'anthropic', 'gemini', or 'mock')
        fallback: Whether to fall back to other providers if the requested one is unavailable
        **kwargs: Additional arguments to pass to the adapter constructor, including:
            - api_key: API key for the provider
            - model: Model to use (e.g., 'gpt-4.1-2025-04-14', 'claude-3-7-sonnet-20250219',
                   'gemini-2.0-flash', 'gemini-2.5-pro-exp-03-25')
            - temperature: Controls randomness (0.0 = deterministic, 1.0 = creative)
            - max_tokens: Maximum number of tokens to generate
            - base_url: Base URL for API requests (for Azure OpenAI or proxies)
            - api_version: API version (required for Azure OpenAI)
            - organization: OpenAI organization ID

    Returns:
        An instance of LLMAdapter (OpenAIAdapter, AnthropicAdapter, GeminiAdapter, or MockAdapter)

    Raises:
        ValueError: If the provider is unknown or unavailable and fallback is False
        ImportError: If the required dependencies are not installed and fallback is False

    Example:
        >>> adapter = get_llm_adapter("openai", api_key="your-api-key", model="gpt-4")
        >>> response = await adapter.chat([{"role": "user", "content": "Hello!"}])
    """
    # Check for development mode - only use mock if explicitly requested or in development mode with mock provider
    import os
    if os.getenv("DEVELOPMENT_MODE", "false").lower() == "true" and provider == "mock":
        logger.info("Running in development mode with mock adapter")
        return MockAdapter(**kwargs)
    # Make sure the registry is populated
    get_llm_adapter_factory()

    # Try to create the requested adapter
    try:
        if provider in ADAPTER_REGISTRY:
            return ADAPTER_REGISTRY[provider](**kwargs)
        else:
            if fallback:
                logger.warning(
                    f"Unknown provider '{provider}', falling back to mock adapter"
                )
                return ADAPTER_REGISTRY["mock"](**kwargs)
            else:
                raise ValueError(f"Unknown LLM provider: {provider}")
    except ImportError as e:
        if not fallback:
            raise

        # If fallback is enabled, try other providers
        logger.warning(
            f"Provider '{provider}' unavailable: {str(e)}. Trying fallback providers."
        )

        fallback_errors = []

        # Try each registered adapter in order (except the one that failed)
        for adapter_name, adapter_class in ADAPTER_REGISTRY.items():
            # Skip the provider that already failed and the mock adapter (we'll use it as last resort)
            if adapter_name == provider or adapter_name == "mock":
                continue

            # Check if the adapter's dependencies are available
            if adapter_name == "openai" and not OPENAI_AVAILABLE:
                continue
            if adapter_name == "anthropic" and not ANTHROPIC_AVAILABLE:
                continue
            if adapter_name == "gemini" and not GEMINI_AVAILABLE:
                continue

            try:
                logger.info(f"Falling back to {adapter_name} adapter")
                return adapter_class(**kwargs)
            except Exception as e2:
                fallback_errors.append(f"{adapter_name} fallback failed: {str(e2)}")

        # Log all fallback errors in a single message if everything failed
        if fallback_errors:
            logger.warning(
                f"All fallbacks failed: {'; '.join(fallback_errors)}. Using mock adapter as last resort."
            )

        # Last resort: Mock adapter
        return ADAPTER_REGISTRY["mock"](**kwargs)


def get_llm_adapter_factory() -> Dict[str, Type[LLMAdapter]]:
    """
    Get a dictionary of available LLM adapter factory functions.

    This can be used for dynamic loading of adapters via configuration or CLI tools.
    The returned dictionary maps provider names to their adapter classes, which can be
    instantiated with appropriate parameters.

    Example:
        >>> factory = get_llm_adapter_factory()
        >>> adapter_class = factory["openai"]
        >>> adapter = adapter_class(api_key="your-api-key")

    Returns:
        A dictionary mapping provider names ("openai", "anthropic", "gemini", "mock") to their
        respective adapter classes (OpenAIAdapter, AnthropicAdapter, GeminiAdapter, MockAdapter).
    """
    # Register built-in adapters if they haven't been registered yet
    if "openai" not in ADAPTER_REGISTRY and OPENAI_AVAILABLE:
        register_adapter("openai", OpenAIAdapter)
    if "anthropic" not in ADAPTER_REGISTRY and ANTHROPIC_AVAILABLE:
        register_adapter("anthropic", AnthropicAdapter)
    if "gemini" not in ADAPTER_REGISTRY and GEMINI_AVAILABLE:
        register_adapter("gemini", GeminiAdapter)
    if "mock" not in ADAPTER_REGISTRY:
        register_adapter("mock", MockAdapter)

    # Return a copy of the registry to prevent modification
    return ADAPTER_REGISTRY.copy()
