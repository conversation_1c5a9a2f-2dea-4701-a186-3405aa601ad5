"""
LLM Adapter Base Classes and Types

This module defines the base classes and types for LLM adapters.
"""

import logging  # For logging metrics and errors
import time  # For tracking latency of LLM calls
from abc import ABC, abstractmethod  # To define the abstract LLMAdapter base class
from functools import wraps  # To preserve metadata when decorating methods
from typing import (  # Type hints for static analysis and better tooling support
    AsyncIterator,  # For streaming async responses from chat
    Dict,  # Generic mapping type (used in metrics or config)
    List,  # For lists of chat messages
    Literal,  # For fixed string values like 'user', 'assistant', 'system'
    Optional,  # For values that may be None
    Tuple,  # For fixed-length token count returns
    TypedDict,  # For defining structured dictionaries like ChatMessage and LLMConfig
    Union,  # For functions returning multiple possible types (e.g., string or stream)
)

logger = logging.getLogger(__name__)  # Module-level logger for capturing adapter metrics and errors


class ChatMessage(TypedDict):
    """
    Standard chat message format.

    Attributes:
        role: The role of the message sender (system, user, assistant)
        content: The content of the message
    """
    # Literal restricts role to strings 'user', 'assistant', or 'system'
    role: Literal["user", "assistant", "system"]
    content: str


class LLMConfig(TypedDict, total=False):
    """
    Configuration options for LLM adapters.

    This TypedDict centralizes configuration options for LLM adapters,
    making it easier to validate and document configuration parameters.
    The 'total=False' means all fields are optional.

    Attributes:
        api_key: API key for the LLM provider
        model: Model name to use
        temperature: Determnistic vs. creative output (0.0 to 1.0)
        max_tokens: Maximum tokens to generate
        timeout: Request timeout in seconds
        max_retries: Maximum number of retries
        streaming: Whether to stream the response
        base_url: Base URL for the API (for custom endpoints)
        api_version: API version to use
        organization: Organization ID (for OpenAI)
    """

    api_key: Optional[str]
    model: str
    temperature: float
    max_tokens: Optional[int]
    timeout: float
    max_retries: int
    streaming: bool
    base_url: Optional[str]
    api_version: Optional[str]
    organization: Optional[str]


def log_llm_metrics(func):
    """
    Decorator to log LLM API call metrics.
    """

    @wraps(func)
    async def wrapper(self, *args, **kwargs):
        start_time = time.time()
        provider = self.__class__.__name__.replace("Adapter", "")

        # Extract attempt number if provided
        attempt = kwargs.get("attempt", 1)
        streaming = kwargs.get("stream", getattr(self, "streaming", False))

        # Get provider-specific config
        provider_config = {}
        for attr in ["base_url", "api_version", "organization"]:
            if hasattr(self, attr) and getattr(self, attr):
                provider_config[attr] = getattr(self, attr)

        try:
            result = await func(self, *args, **kwargs)

            # Calculate metrics
            elapsed_time = time.time() - start_time

            # Prepare metrics dictionary
            metrics = {
                "provider": provider,
                "model": getattr(self, "model", "unknown"),
                "latency_ms": round(elapsed_time * 1000, 2),
                "success": True,
                "attempt": attempt,
                "streaming": streaming,
            }

            # Add provider config if available
            if provider_config:
                metrics["provider_config"] = provider_config

            # Log success metrics
            logger.info(
                f"LLM API call completed: {provider}", extra={"metrics": metrics}
            )

            return result
        except Exception as e:
            # Calculate error metrics
            elapsed_time = time.time() - start_time

            # Prepare metrics dictionary
            metrics = {
                "provider": provider,
                "model": getattr(self, "model", "unknown"),
                "latency_ms": round(elapsed_time * 1000, 2),
                "success": False,
                "attempt": attempt,
                "streaming": streaming,
                "error_type": type(e).__name__,
                "error_message": str(e),
            }

            # Add provider config if available
            if provider_config:
                metrics["provider_config"] = provider_config

            # Log error metrics with traceback
            logger.exception(
                f"Error calling {provider} API: {str(e)}", extra={"metrics": metrics}
            )
            raise

    return wrapper


class LLMAdapter(ABC):
    """
    Abstract base class for LLM adapters.

    This class defines the interface that all LLM adapters must implement.
    It provides a unified interface for interacting with different LLM providers
    like OpenAI and Anthropic, abstracting away the differences between them.

    Concrete implementations must override the abstract methods:
    - chat(): Send a chat request to the LLM
    - get_token_count(): Count the number of tokens in the messages

    The class also provides two properties:
    - client: Access to the underlying client instance
    - supports_streaming: Whether streaming is supported
    """

    @abstractmethod
    async def chat(
        self, messages: List[ChatMessage], **kwargs
    ) -> Union[str, AsyncIterator[str]]:
        """
        Send a chat request to the LLM and get a response.

        Args:
            messages: List of message dictionaries with 'role' and 'content' keys
            **kwargs: Additional arguments to pass to the LLM API

        Returns:
            The LLM's response as a string, or an async iterator yielding chunks if streaming is enabled
        """
        pass

    @abstractmethod
    async def get_token_count(self, messages: List[ChatMessage]) -> Tuple[int, int]:
        """
        Count the number of tokens in the messages.

        Args:
            messages: List of message dictionaries with 'role' and 'content' keys

        Returns:
            A tuple of (prompt_tokens, estimated_response_tokens)
        """
        pass

    @property
    def client(self):
        """
        Get the underlying client instance.

        This property provides access to the raw client for advanced operations.
        Not all adapters may implement this property.

        Returns:
            The underlying client instance or None if not available
        """
        return getattr(self, "_client", None)

    @property
    def supports_streaming(self) -> bool:
        """
        Whether this adapter supports streaming responses.

        Returns:
            True if streaming is supported, False otherwise
        """
        return False

    def get_stream_flag(self, stream: bool = False) -> bool:
        """
        Get the appropriate stream flag based on adapter capabilities.

        Args:
            stream: Whether streaming was requested

        Returns:
            Boolean indicating whether to stream the response (True only if
            streaming is both requested and supported by the adapter)
        """
        # Only return True if streaming is both requested and supported
        return stream and self.supports_streaming
