"""
Anthropic Adapter Implementation

This module implements the Anthropic adapter for the LLM interface.
"""

import asyncio
import logging
import os
from typing import AsyncIterator, Dict, List, Literal, Optional, Tuple, Union

from .base import ChatMessage, LLMAdapter, log_llm_metrics

logger = logging.getLogger(__name__)

# Anthropic dependencies:
try:
    import anthropic
    from anthropic import AsyncAnthropic
    # if imports work, "flag" is created to indicate that ANTHROPIC is available (i.e., is True):
    ANTHROPIC_AVAILABLE = True
except ImportError:
    # if imports fail, set variables to None and create "flag" to indicate that ANTHROPIC is not available (i.e., is False):
    anthropic = None
    AsyncAnthropic = None
    ANTHROPIC_AVAILABLE = False

# Tenacity for retry logic:
try:
    # Try to import the Tenacity functions for retrying failed operations
    from tenacity import (retry,
                          retry_if_exception_type,
                          stop_after_attempt,
                          wait_exponential)
    # If imports work, set flag to True to indicate Tenacity is available
    TENACITY_AVAILABLE = True

except ImportError:
    # If Tenacity is not installed, define fallback dummy versions of the functions
    # These dummy functions do nothing, but prevent runtime errors in code that expects them

    # 'retry' becomes a decorator that returns the original function unmodified
    retry = lambda *_args, **_kwargs: lambda func: func  # noqa: E731, F841

    # 'retry_if_exception_type' becomes a placeholder that does nothing
    retry_if_exception_type = lambda *_args: None  # noqa: E731, F841

    # 'stop_after_attempt' becomes a placeholder that does nothing
    stop_after_attempt = lambda *_args: None  # noqa: E731, F841

    # 'wait_exponential' becomes a placeholder that does nothing
    wait_exponential = lambda *_args, **_kwargs: None  # noqa: E731, F841

    # Set flag to False to indicate Tenacity is not available
    TENACITY_AVAILABLE = False

    # Log a warning to flag that retry logic will be skipped
    logger.warning("Tenacity not installed. Retries will not be attempted.")


class AnthropicAdapter(LLMAdapter):
    """
    Adapter for Anthropic's Claude models.
    """

    def __init__(
        self,
        model: str = "claude-3-7-sonnet-20250219",
        api_key: Optional[str] = None,
        **kwargs,
    ):
        """
        Initialize the Anthropic adapter.

        Args:
            model: The model to use (default: claude-3-7-sonnet-20250219)
            api_key: Anthropic API key (default: from environment)
            **kwargs: Additional configuration options including:
                - timeout: Request timeout in seconds (default: 30.0)
                - max_retries: Maximum number of retries (default: 3)
                - temperature: Sampling temperature (default: 0.7)
                - max_tokens: Maximum tokens to generate (default: 1000)
                - base_url: Base URL for API requests (for proxies)
                - api_version: API version to use (for proxies)
        """
        if not ANTHROPIC_AVAILABLE:
            raise ImportError(
                "Anthropic package is required. Install with 'pip install anthropic'."
            )

        self.model = model
        self.api_key = api_key or os.getenv("ANTHROPIC_API_KEY")
        if not self.api_key:
            raise ValueError("Anthropic API key is required")

        # Additional configuration
        self.timeout = kwargs.get("timeout", 30.0)
        self.max_retries = kwargs.get("max_retries", 3)
        self.temperature = kwargs.get("temperature", 0.7)
        self.max_tokens = kwargs.get("max_tokens", 1000)

        # Proxy configuration
        self.base_url = kwargs.get("base_url", None)
        self.api_version = kwargs.get("api_version", None)

        # Initialize the client once
        client_kwargs = {"api_key": self.api_key, "timeout": self.timeout}

        # Add optional configuration if provided
        if self.base_url:
            client_kwargs["base_url"] = self.base_url
        if self.api_version:
            client_kwargs["api_version"] = self.api_version

        # Create the async client
        self._client = AsyncAnthropic(**client_kwargs)

        logger.info(f"Anthropic adapter initialized with model: {model}")

    @log_llm_metrics
    async def chat(
        self, messages: List[ChatMessage], **kwargs
    ) -> Union[str, AsyncIterator[str]]:
        """
        Send a chat request to Anthropic and get a response.

        Args:
            messages: List of message dictionaries with 'role' and 'content' keys
            **kwargs: Additional arguments to pass to the Anthropic API
                - temperature: Sampling temperature (default: self.temperature)
                - max_tokens: Maximum tokens to generate (default: self.max_tokens)
                - stream: Whether to stream the response (default: False)

        Returns:
            The model's response as a string, or an async iterator yielding chunks if streaming is enabled
        """
        if not ANTHROPIC_AVAILABLE:
            raise ImportError(
                "Anthropic package is required. Install with 'pip install anthropic'."
            )

        # Merge instance config with call-specific kwargs
        temperature = kwargs.get("temperature", self.temperature)
        max_tokens = kwargs.get("max_tokens", self.max_tokens)
        stream = self.get_stream_flag(kwargs.get("stream", False))

        # Handle streaming separately
        if stream:
            # Create and return an async generator directly
            async def generate_stream():
                try:
                    # Convert messages to Anthropic format
                    anthropic_messages = self._convert_to_anthropic_format(messages)

                    # Make the streaming API call
                    stream = await self._client.messages.create(
                        model=self.model,
                        messages=anthropic_messages,
                        max_tokens=max_tokens,
                        temperature=temperature,
                        stream=True,
                    )

                    # Yield content chunks as they arrive
                    async for chunk in stream:
                        if chunk.type == "content_block_delta" and chunk.delta.text:
                            yield chunk.delta.text
                except Exception as e:
                    logger.error(f"Error in streaming Anthropic API call: {e}")
                    raise

            return generate_stream()

        # Apply retry logic if tenacity is available for non-streaming requests
        if TENACITY_AVAILABLE:
            return await self._chat_with_retry(messages, temperature, max_tokens)
        else:
            return await self._chat_without_retry(messages, temperature, max_tokens)

    async def _chat_with_retry(self, messages, temperature, max_tokens):
        """Chat with retry logic using tenacity."""
        if TENACITY_AVAILABLE:
            # Define a callback to track retry attempts
            attempt_count = 1

            def before_retry_callback(_):
                nonlocal attempt_count
                attempt_count += 1
                logger.debug(
                    f"Tenacity retry {attempt_count}/{self.max_retries} for Anthropic call"
                )
                return None

            @retry(
                stop=stop_after_attempt(self.max_retries),
                wait=wait_exponential(multiplier=1, min=2, max=10),
                retry=retry_if_exception_type(
                    (
                        anthropic.APIError,
                        anthropic.APIConnectionError,
                        anthropic.RateLimitError,
                        anthropic.APITimeoutError,
                    )
                ),
                reraise=True,
            )
            async def _call_api():
                # Pass current attempt count for metrics
                return await self._chat_without_retry(
                    messages, temperature, max_tokens, attempt=attempt_count
                )

            return await _call_api()
        else:
            # Manual retry logic as fallback when tenacity is not available
            attempt = 0
            last_exception = None

            while attempt < self.max_retries:
                try:
                    # Pass attempt number for metrics
                    return await self._chat_without_retry(
                        messages, temperature, max_tokens, attempt=attempt + 1
                    )
                except (
                    anthropic.APIError,
                    anthropic.APIConnectionError,
                    anthropic.RateLimitError,
                    anthropic.APITimeoutError,
                ) as e:
                    attempt += 1
                    last_exception = e

                    if attempt >= self.max_retries:
                        break

                    # Check if this is a rate limit error
                    error_message = str(e).lower()
                    is_rate_limit = any(term in error_message for term in ["rate limit", "ratelimit", "too many requests", "429"])

                    # Exponential backoff
                    wait_time = min(10, 1 * (2**attempt))

                    # Add guidance for rate limit errors
                    if is_rate_limit:
                        logger.warning(
                            f"Rate limit hit for Anthropic API. Consider: "
                            f"1) Reducing message history length, "
                            f"2) Using a smaller model variant, or "
                            f"3) Implementing request queuing. "
                            f"Retry attempt {attempt}/{self.max_retries}. Waiting {wait_time}s..."
                        )
                    else:
                        logger.warning(
                            f"Retry attempt {attempt}/{self.max_retries} after error: {str(e)}. Waiting {wait_time}s..."
                        )
                    await asyncio.sleep(wait_time)
                except Exception as e:
                    # Don't retry other types of exceptions
                    raise

            # If we've exhausted all retries, log and raise the last exception
            if last_exception:
                logger.error(
                    f"Max retries ({self.max_retries}) reached for Anthropic call",
                    exc_info=last_exception,
                )
                raise last_exception

    @property
    def supports_streaming(self) -> bool:
        """
        Whether this adapter supports streaming responses.

        Returns:
            True if streaming is supported, False otherwise
        """
        return True

    async def _chat_without_retry(
        self, messages, temperature, max_tokens, attempt: int = 1
    ):
        """Chat without retry logic."""
        try:
            # Log attempt number for debugging
            if attempt > 1:
                logger.debug(
                    f"Attempt {attempt} for Anthropic API call with model {self.model}"
                )

            # Convert messages to Anthropic format
            anthropic_messages = self._convert_to_anthropic_format(messages)

            # Make the API call using the async client
            response = await self._client.messages.create(
                model=self.model,
                messages=anthropic_messages,
                max_tokens=max_tokens,
                temperature=temperature,
            )

            # Return the response
            return response.content[0].text
        except Exception as e:
            logger.error(f"Error calling Anthropic API: {e}")
            raise

    def _convert_to_anthropic_format(
        self, messages: List[ChatMessage]
    ) -> List[Dict[Literal["role", "content"], str]]:
        """
        Convert standard chat messages to Anthropic format.

        Args:
            messages: List of message dictionaries with 'role' and 'content' keys

        Returns:
            List of messages in Anthropic format
        """
        # Filter out empty messages
        messages = [msg for msg in messages if msg.get("content", "").strip()]
        if not messages:
            return [
                {"role": "user", "content": "Hello"}
            ]  # Fallback to ensure we have at least one message

        anthropic_messages = []
        system_message = None

        # First, extract the first system message (ignore others)
        system_messages = [msg for msg in messages if msg["role"] == "system"]
        if system_messages:
            system_message = system_messages[0]["content"]
            # Log if multiple system messages were found
            if len(system_messages) > 1:
                logger.warning(
                    f"Multiple system messages found, using only the first one. Ignored {len(system_messages)-1} additional system messages."
                )

        # Then process all messages
        for msg in messages:
            role = msg["role"]

            if role == "user":
                content = msg["content"]
                # If we have a system message and this is the first user message,
                # prepend the system message
                if system_message and not any(
                    m["role"] == "user" for m in anthropic_messages
                ):
                    content = f"System: {system_message}\n\n{content}"
                anthropic_messages.append({"role": "user", "content": content})
            elif role == "assistant":
                anthropic_messages.append(
                    {"role": "assistant", "content": msg["content"]}
                )
            # Skip system messages as they're handled separately

        # Ensure we have at least one message and the first message is from the user
        # (Anthropic requires the first message to be from the user)
        if not anthropic_messages:
            if system_message:
                anthropic_messages.append(
                    {"role": "user", "content": f"System: {system_message}"}
                )
            else:
                anthropic_messages.append({"role": "user", "content": "Hello"})
        elif anthropic_messages[0]["role"] != "user":
            # If the first message is not from the user, prepend a user message
            if system_message:
                anthropic_messages.insert(
                    0,
                    {"role": "user", "content": f"System: {system_message}"},
                )
            else:
                anthropic_messages.insert(0, {"role": "user", "content": "Hello"})

        return anthropic_messages

    async def get_token_count(self, messages: List[ChatMessage], verbose: bool = False) -> Tuple[int, int]:
        """
        Count the number of tokens in the messages.

        Args:
            messages: List of message dictionaries with 'role' and 'content' keys
            verbose: Whether to log detailed token information for each message

        Returns:
            A tuple of (prompt_tokens, estimated_response_tokens)
        """
        try:
            # Convert to Anthropic format first
            anthropic_messages = self._convert_to_anthropic_format(messages)

            # Prepare message details for verbose logging
            message_details = []
            if verbose:
                for i, (orig_msg, anth_msg) in enumerate(zip(messages, anthropic_messages)):
                    message_details.append({
                        "index": i,
                        "role": orig_msg.get("role", "unknown"),
                        "content_preview": orig_msg.get("content", "")[:30] + "...",
                        "anthropic_role": anth_msg.get("role", "unknown"),
                        "anthropic_content_preview": anth_msg.get("content", "")[:30] + "..."
                    })

            token_source = "unknown"
            try:
                # Use the built-in token counting from the Anthropic SDK
                # Note: count_tokens is a synchronous method in the Anthropic SDK
                # It doesn't need to be awaited
                prompt_tokens = self._client.count_tokens(messages=anthropic_messages)
                token_source = "anthropic_sdk"
            except Exception as token_error:
                # Fall back to character-based heuristic if SDK token counting fails
                logger.debug(
                    f"Anthropic SDK token counting failed: {token_error}, using character-based heuristic"
                )
                # Claude uses roughly 1 token per 4 characters
                prompt_chars = sum(
                    len(m.get("content", "")) for m in anthropic_messages
                )
                prompt_tokens = prompt_chars // 4
                # Add tokens for message format (roughly 5 tokens per message)
                prompt_tokens += len(anthropic_messages) * 5
                token_source = "character_heuristic"

            # Estimate response tokens (rough estimate)
            estimated_response_tokens = min(500, prompt_tokens)

            # Log detailed token information if verbose is enabled
            if verbose:
                logger.debug(
                    f"Token count details for {self.model}:",
                    extra={
                        "token_details": {
                            "model": self.model,
                            "token_source": token_source,
                            "messages": message_details,
                            "prompt_tokens": prompt_tokens,
                            "estimated_response_tokens": estimated_response_tokens,
                            "estimated_total_tokens": prompt_tokens + estimated_response_tokens
                        }
                    }
                )

            return prompt_tokens, estimated_response_tokens
        except Exception as e:
            logger.error(f"Error counting tokens: {e}")
            return 0, 0
