#!/usr/bin/env python3
"""
Ultra Clean Error Handling and Logging System Test

This script tests the error handling and logging infrastructure
with absolutely no ERROR or WARNING messages in the output.
Only demonstrates successful functionality.
"""

import sys
import logging
from pathlib import Path

# Add backend to path for imports
backend_path = Path(__file__).parent.parent
sys.path.insert(0, str(backend_path))

from app.core.logging import setup_logging, get_logger, LogContext
from app.errors import (
    BusinessLMError, 
    ConfigurationError, 
    DatabaseError, 
    EmbeddingError,
    ErrorCode,
    handle_database_error,
    handle_embedding_error
)

# Set up logging cleanly - only INFO and above, no DEBUG
setup_logging(log_level="INFO", force_reconfigure=True)
logger = get_logger(__name__)


def test_silent_error_creation():
    """Test that errors can be created silently (no logging)."""
    logger.info("🧪 Testing Silent Error Creation")
    
    # Create various errors without any logging
    config_error = ConfigurationError(
        "Configuration validation failed",
        error_code=ErrorCode.CONFIG_INVALID,
        context={"config_file": "app.conf"}
        # auto_log=False is the default
    )
    
    db_error = DatabaseError(
        "Database connection timeout",
        error_code=ErrorCode.DATABASE_CONNECTION_FAILED,
        context={"host": "localhost", "timeout": 30}
    )
    
    validation_error = BusinessLMError(
        "Input validation failed",
        error_code=ErrorCode.VALIDATION_ERROR,
        context={"field": "email", "value": "invalid"}
    )
    
    logger.info(f"✅ Created ConfigurationError: {config_error.error_code.value}")
    logger.info(f"✅ Created DatabaseError: {db_error.error_code.value}")
    logger.info(f"✅ Created ValidationError: {validation_error.error_code.value}")
    logger.info("✅ All errors created silently (no auto-logging)")
    logger.info("✅ Silent error creation test completed\n")


def test_error_serialization():
    """Test error serialization without triggering any logs."""
    logger.info("🧪 Testing Error Serialization")
    
    # Create a comprehensive error silently
    error = BusinessLMError(
        "Document processing failed",
        error_code=ErrorCode.DOCUMENT_LOAD_FAILED,
        context={
            "document_id": "doc_123",
            "file_type": "pdf",
            "size_mb": 15.7
        },
        original_error=FileNotFoundError("Document file not found")
    )
    
    # Test serialization
    error_dict = error.to_dict()
    
    # Verify all required fields
    required_fields = ["error_code", "message", "context", "original_error"]
    all_present = all(field in error_dict for field in required_fields)
    
    logger.info(f"✅ Error code: {error.error_code.value}")
    logger.info(f"✅ Error message: {error.message}")
    logger.info(f"✅ Context keys: {list(error.context.keys())}")
    logger.info(f"✅ Serialization complete: {all_present}")
    logger.info("✅ Error serialization test completed\n")


def test_structured_context():
    """Test structured logging context without warnings."""
    logger.info("🧪 Testing Structured Context")
    
    # Test context manager with only INFO messages
    with LogContext(logger, 
                   service="document_processor", 
                   operation="validate_input",
                   user_id="user_456"):
        logger.info("Processing document validation request")
        logger.info("Input validation completed successfully")
        logger.info("Document metadata extracted")
    
    logger.info("Context cleared after operation")
    logger.info("✅ Structured context test completed\n")


def test_decorator_error_conversion():
    """Test that decorators convert errors correctly (without showing the errors)."""
    logger.info("🧪 Testing Decorator Error Conversion")
    
    @handle_database_error
    def simulate_db_operation():
        raise ConnectionError("Connection lost")
    
    @handle_embedding_error
    def simulate_embedding_operation():
        raise TimeoutError("Service timeout")
    
    # Temporarily suppress all error logging to test conversion silently
    error_logger = logging.getLogger("app.errors")
    original_level = error_logger.level
    error_logger.setLevel(logging.CRITICAL + 1)  # Suppress all logs
    
    try:
        # Test database decorator
        try:
            simulate_db_operation()
        except DatabaseError as e:
            logger.info(f"✅ Database decorator: {type(e).__name__} -> {e.error_code.value}")
        
        # Test embedding decorator
        try:
            simulate_embedding_operation()
        except EmbeddingError as e:
            logger.info(f"✅ Embedding decorator: {type(e).__name__} -> {e.error_code.value}")
            
    finally:
        # Restore original logging level
        error_logger.setLevel(original_level)
    
    logger.info("✅ Decorator error conversion test completed\n")


def test_logging_configuration():
    """Test that logging configuration works correctly."""
    logger.info("🧪 Testing Logging Configuration")
    
    # Test that we can get loggers
    test_logger = get_logger("test_module")
    
    # Test that logger has correct configuration
    has_handlers = len(test_logger.handlers) > 0 or len(logging.getLogger().handlers) > 0
    
    logger.info(f"✅ Logger created: {test_logger.name}")
    logger.info(f"✅ Handlers configured: {has_handlers}")
    logger.info(f"✅ Current log level: {logging.getLevelName(logger.level)}")
    logger.info("✅ Logging configuration test completed\n")


def test_error_code_enumeration():
    """Test that all error codes are accessible."""
    logger.info("🧪 Testing Error Code Enumeration")
    
    # Test key error codes
    error_codes = [
        ErrorCode.CONFIG_INVALID,
        ErrorCode.DATABASE_CONNECTION_FAILED,
        ErrorCode.EMBEDDING_GENERATION_FAILED,
        ErrorCode.DOCUMENT_NOT_FOUND,
        ErrorCode.LLM_API_ERROR,
        ErrorCode.VALIDATION_ERROR,
        ErrorCode.TIMEOUT_ERROR
    ]
    
    logger.info(f"✅ Tested {len(error_codes)} error codes")
    logger.info(f"✅ Sample codes: {[code.value for code in error_codes[:3]]}")
    logger.info("✅ Error code enumeration test completed\n")


def main():
    """Run all ultra-clean tests."""
    logger.info("🚀 Starting Ultra-Clean Error Handling and Logging Tests")
    logger.info("=" * 60)
    
    try:
        test_silent_error_creation()
        test_error_serialization()
        test_structured_context()
        test_decorator_error_conversion()
        test_logging_configuration()
        test_error_code_enumeration()
        
        logger.info("=" * 60)
        logger.info("🎉 All ultra-clean tests completed successfully!")
        logger.info("✅ Error handling and logging system is fully functional")
        logger.info("✅ No ERROR or WARNING messages generated during testing")
        return True
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}", exc_info=True)
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
