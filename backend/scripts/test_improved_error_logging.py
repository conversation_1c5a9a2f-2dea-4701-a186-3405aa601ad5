#!/usr/bin/env python3
"""
Test Improved Error Handling and Logging System

This script tests the improved error handling and logging infrastructure
with better control over when errors are logged.
"""

import sys
import logging
from pathlib import Path

# Add backend to path for imports
backend_path = Path(__file__).parent.parent
sys.path.insert(0, str(backend_path))

from app.core.logging import setup_logging, get_logger, LogContext
from app.errors import (
    BusinessLMError,
    ConfigurationError,
    DatabaseError,
    EmbeddingError,
    ErrorCode,
    handle_database_error,
    handle_embedding_error
)

# Set up logging once with force_reconfigure to override default
setup_logging(log_level="DEBUG", force_reconfigure=True)
logger = get_logger(__name__)


def test_controlled_error_logging():
    """Test that errors are only logged when explicitly requested."""
    logger.info("🧪 Testing controlled error logging")

    # Test error without auto-logging (default behavior)
    try:
        raise BusinessLMError(
            "This error should NOT be auto-logged",
            error_code=ErrorCode.UNKNOWN_ERROR,
            context={"test": "no_auto_log"}
        )
    except BusinessLMError as e:
        logger.info(f"✅ Error created without auto-logging: {e}")

    # Test error with auto-logging enabled
    try:
        raise ConfigurationError(
            "This error SHOULD be auto-logged",
            error_code=ErrorCode.CONFIG_INVALID,
            context={"test": "with_auto_log"},
            auto_log=True
        )
    except ConfigurationError as e:
        logger.info(f"✅ Error created with auto-logging: {e}")

    logger.info("✅ Controlled error logging test completed\n")


def test_decorator_error_logging():
    """Test that decorator errors are properly logged."""
    logger.info("🧪 Testing decorator error logging")

    @handle_database_error
    def failing_db_operation():
        raise Exception("Simulated database failure")

    @handle_embedding_error
    def failing_embedding_operation():
        raise Exception("Simulated embedding failure")

    # These should auto-log because decorators enable auto_log=True
    try:
        failing_db_operation()
    except DatabaseError as e:
        logger.info(f"✅ Database decorator error (auto-logged): {e}")

    try:
        failing_embedding_operation()
    except EmbeddingError as e:
        logger.info(f"✅ Embedding decorator error (auto-logged): {e}")

    logger.info("✅ Decorator error logging test completed\n")


def test_structured_context_logging():
    """Test structured logging with context."""
    logger.info("🧪 Testing structured context logging")

    # Test context manager
    with LogContext(logger, operation="test_operation", user_id="test_user"):
        logger.info("Message with structured context")
        logger.warning("Warning with structured context")

    logger.info("Message without context (after context manager)")

    logger.info("✅ Structured context logging test completed\n")


def test_error_serialization():
    """Test error serialization and representation."""
    logger.info("🧪 Testing error serialization")

    error = BusinessLMError(
        "Test serialization error",
        error_code=ErrorCode.VALIDATION_ERROR,
        context={"field": "test_field", "value": "invalid_value"},
        original_error=ValueError("Original validation error")
    )

    # Test string representation
    logger.info(f"String representation: {error}")

    # Test dictionary serialization
    error_dict = error.to_dict()
    logger.info(f"Dictionary representation: {error_dict}")

    # Verify all expected fields are present
    expected_fields = ["error_code", "message", "context", "original_error"]
    for field in expected_fields:
        assert field in error_dict, f"Missing field: {field}"

    logger.info("✅ Error serialization test completed\n")


def test_logging_levels():
    """Test different logging levels with enhanced formatter."""
    logger.info("🧪 Testing logging levels with enhanced formatter")

    logger.debug("Debug message - detailed information")
    logger.info("Info message - general information")
    logger.warning("Warning message - something unexpected happened")
    logger.error("Error message - something went wrong")
    logger.critical("Critical message - serious error occurred")

    logger.info("✅ Logging levels test completed\n")


def test_performance_logging():
    """Test performance logging functionality."""
    logger.info("🧪 Testing performance logging")

    from app.core.logging import log_performance

    @log_performance(logger, threshold_seconds=0.05)
    def fast_function():
        import time
        time.sleep(0.01)  # Fast operation
        return "fast_result"

    @log_performance(logger, threshold_seconds=0.05)
    def slow_function():
        import time
        time.sleep(0.1)  # Slow operation
        return "slow_result"

    # Fast function should not trigger performance warning
    result1 = fast_function()
    logger.info(f"Fast function result: {result1}")

    # Slow function should trigger performance warning
    result2 = slow_function()
    logger.info(f"Slow function result: {result2}")

    logger.info("✅ Performance logging test completed\n")


def main():
    """Run all improved tests."""
    logger.info("🚀 Starting Improved Error Handling and Logging Tests")
    logger.info("=" * 60)

    try:
        test_controlled_error_logging()
        test_decorator_error_logging()
        test_structured_context_logging()
        test_error_serialization()
        test_logging_levels()
        test_performance_logging()

        logger.info("=" * 60)
        logger.info("🎉 All improved tests completed successfully!")
        logger.info("✅ Error handling and logging system is working correctly")
        return True

    except Exception as e:
        logger.error(f"❌ Test failed: {e}", exc_info=True)
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
