#!/usr/bin/env python3
"""
Common Script Utilities

This module provides common utilities for all backend scripts to eliminate
code duplication and standardize script patterns.
"""

import sys
import logging
from pathlib import Path
from typing import Optional


def setup_script_environment() -> Path:
    """
    Set up the script environment by adding backend to Python path.
    
    This function ensures that all scripts can import backend modules
    regardless of where they're run from.
    
    Returns:
        Path to the backend directory
    """
    backend_path = Path(__file__).parent.parent
    if str(backend_path) not in sys.path:
        sys.path.insert(0, str(backend_path))
    return backend_path


def setup_script_logging(
    script_name: str, 
    log_level: str = "INFO",
    include_timestamp: bool = True
) -> logging.Logger:
    """
    Set up standardized logging for scripts.
    
    Args:
        script_name: Name of the script (usually __name__)
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        include_timestamp: Whether to include timestamp in log format
        
    Returns:
        Configured logger instance
    """
    # Create formatter
    if include_timestamp:
        format_string = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    else:
        format_string = "%(name)s - %(levelname)s - %(message)s"
    
    # Configure logging
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format=format_string,
        handlers=[logging.StreamHandler(sys.stdout)],
        force=True  # Override any existing configuration
    )
    
    return logging.getLogger(script_name)


def initialize_database() -> bool:
    """
    Initialize database connection for scripts.
    
    This function provides a common way for scripts to initialize
    the database connection with proper error handling.
    
    Returns:
        True if initialization successful, False otherwise
    """
    try:
        from app.core.db.database import init_db, check_database_connection
        
        # Initialize database
        init_db()
        
        # Verify connection
        if check_database_connection():
            return True
        else:
            return False
            
    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.error(f"Database initialization failed: {e}")
        return False


def get_script_config():
    """
    Get configuration for scripts with proper error handling.
    
    Returns:
        Settings instance or None if failed
    """
    try:
        from app.config import get_settings
        return get_settings()
    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.error(f"Failed to load configuration: {e}")
        return None


def validate_script_prerequisites(
    require_database: bool = True,
    require_config: bool = True,
    logger: Optional[logging.Logger] = None
) -> bool:
    """
    Validate that script prerequisites are met.
    
    Args:
        require_database: Whether database connection is required
        require_config: Whether configuration loading is required
        logger: Logger instance to use for messages
        
    Returns:
        True if all prerequisites are met, False otherwise
    """
    if logger is None:
        logger = logging.getLogger(__name__)
    
    # Check configuration
    if require_config:
        config = get_script_config()
        if config is None:
            logger.error("❌ Configuration loading failed")
            return False
        logger.info("✅ Configuration loaded successfully")
    
    # Check database
    if require_database:
        if not initialize_database():
            logger.error("❌ Database initialization failed")
            return False
        logger.info("✅ Database initialized successfully")
    
    return True


class ScriptContext:
    """
    Context manager for script setup and cleanup.
    
    This provides a convenient way to set up the environment,
    logging, and prerequisites for a script.
    """
    
    def __init__(
        self,
        script_name: str,
        log_level: str = "INFO",
        require_database: bool = True,
        require_config: bool = True
    ):
        """
        Initialize script context.
        
        Args:
            script_name: Name of the script
            log_level: Logging level to use
            require_database: Whether database is required
            require_config: Whether configuration is required
        """
        self.script_name = script_name
        self.log_level = log_level
        self.require_database = require_database
        self.require_config = require_config
        self.logger = None
        self.backend_path = None
    
    def __enter__(self):
        """Enter script context."""
        # Set up environment
        self.backend_path = setup_script_environment()
        
        # Set up logging
        self.logger = setup_script_logging(self.script_name, self.log_level)
        
        # Validate prerequisites
        if not validate_script_prerequisites(
            require_database=self.require_database,
            require_config=self.require_config,
            logger=self.logger
        ):
            raise RuntimeError("Script prerequisites not met")
        
        return self.logger
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Exit script context."""
        if exc_type is not None:
            self.logger.error(f"Script failed with {exc_type.__name__}: {exc_val}")
        else:
            self.logger.info("Script completed successfully")


def run_script_with_error_handling(script_func, script_name: str, *args, **kwargs):
    """
    Run a script function with standardized error handling.
    
    Args:
        script_func: Function to run
        script_name: Name of the script for logging
        *args: Arguments to pass to script_func
        **kwargs: Keyword arguments to pass to script_func
        
    Returns:
        Exit code (0 for success, 1 for failure)
    """
    try:
        with ScriptContext(script_name) as logger:
            logger.info(f"🚀 Starting {script_name}")
            
            result = script_func(*args, **kwargs)
            
            if result is False:
                logger.error("❌ Script execution failed")
                return 1
            else:
                logger.info("🎉 Script execution completed successfully")
                return 0
                
    except Exception as e:
        # Fallback logging if context setup failed
        logging.basicConfig(level=logging.ERROR)
        logger = logging.getLogger(script_name)
        logger.error(f"❌ Script failed: {e}")
        return 1


# Convenience functions for common script patterns

def script_main(script_func):
    """
    Decorator to wrap a script main function with error handling.
    
    Usage:
        @script_main
        def main():
            # Your script logic here
            pass
            
        if __name__ == "__main__":
            main()
    """
    def wrapper(*args, **kwargs):
        script_name = script_func.__module__
        return run_script_with_error_handling(script_func, script_name, *args, **kwargs)
    
    return wrapper


def get_database_session():
    """
    Get a database session for scripts.
    
    Returns:
        Database session context manager
    """
    from app.core.db.database import get_db_context
    return get_db_context()


def ensure_pgvector_extension():
    """
    Ensure pgvector extension is available.
    
    Returns:
        True if successful, False otherwise
    """
    try:
        from app.core.db.database import ensure_pgvector_extension as _ensure_pgvector
        _ensure_pgvector()
        return True
    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.error(f"Failed to ensure pgvector extension: {e}")
        return False
