#!/usr/bin/env python
"""
Database Migration Management Script

This script provides utilities for managing database migrations using Alembic.

Usage:
    python manage_migrations.py <command> [options]

Commands:
    status      Show migration status
    migrate     Run pending migrations
    rollback    Rollback to previous migration
    create      Create a new migration
    reset       Reset database (drop and recreate)

Examples:
    python manage_migrations.py status
    python manage_migrations.py migrate
    python manage_migrations.py rollback
    python manage_migrations.py create "Add new column"
    python manage_migrations.py reset --confirm
"""

import argparse
import sys

# Import common script utilities
from script_utils import (
    setup_script_environment,
    setup_script_logging,
    ScriptContext,
    script_main
)

# Set up environment
setup_script_environment()

# Import migration utilities
from app.core.db.migrations import (
    get_migration_status,
    run_migrations,
    rollback_migration,
    create_migration,
    initialize_migrations,
    get_current_revision,
    get_available_revisions
)
from app.core.db.database import (
    reset_database,
    get_table_info,
    check_database_connection
)


def show_status(logger=None):
    """Show migration status."""
    if logger is None:
        logger = setup_script_logging(__name__)

    logger.info("📊 Checking migration status...")

    try:
        # Database initialization handled by ScriptContext
        # Check connection
        if not check_database_connection():
            logger.error("❌ Database connection failed")
            return False

        # Get migration status
        status = get_migration_status()

        if status.get("error"):
            logger.error(f"❌ Error getting migration status: {status['error']}")
            return False

        # Display status
        logger.info("📋 Migration Status:")
        logger.info(f"  Current revision: {status['current_revision'] or 'None'}")
        logger.info(f"  Available revisions: {len(status['available_revisions'])}")
        logger.info(f"  Up to date: {'✅' if status['is_up_to_date'] else '❌'}")

        if status['pending_migrations']:
            logger.info(f"  Pending migrations: {len(status['pending_migrations'])}")
            for revision in status['pending_migrations']:
                logger.info(f"    - {revision}")
        else:
            logger.info("  No pending migrations")

        # Show table info
        table_info = get_table_info()
        logger.info("📊 Database Info:")
        logger.info(f"  Documents: {table_info.get('documents_count', 0)}")
        logger.info(f"  Document chunks: {table_info.get('document_chunks_count', 0)}")
        logger.info(f"  pgvector installed: {table_info.get('pgvector_installed', False)}")

        return True

    except Exception as e:
        logger.error(f"❌ Failed to get migration status: {e}")
        return False


def migrate(logger=None):
    """Run pending migrations."""
    if logger is None:
        logger = setup_script_logging(__name__)

    logger.info("🚀 Running migrations...")

    try:
        if initialize_migrations():
            logger.info("✅ Migrations completed successfully")
            return True
        else:
            logger.error("❌ Migration failed")
            return False

    except Exception as e:
        logger.error(f"❌ Migration failed: {e}")
        return False


def rollback(logger=None):
    """Rollback to previous migration."""
    if logger is None:
        logger = setup_script_logging(__name__)

    logger.info("⏪ Rolling back migration...")

    try:
        if rollback_migration():
            logger.info("✅ Rollback completed successfully")
            return True
        else:
            logger.error("❌ Rollback failed")
            return False

    except Exception as e:
        logger.error(f"❌ Rollback failed: {e}")
        return False


def create_new_migration(message: str, logger=None):
    """Create a new migration."""
    if logger is None:
        logger = setup_script_logging(__name__)

    logger.info(f"📝 Creating migration: {message}")

    try:
        if create_migration(message):
            logger.info("✅ Migration created successfully")
            return True
        else:
            logger.error("❌ Failed to create migration")
            return False

    except Exception as e:
        logger.error(f"❌ Failed to create migration: {e}")
        return False


def reset_db(confirm: bool = False, logger=None):
    """Reset the database."""
    if logger is None:
        logger = setup_script_logging(__name__)

    if not confirm:
        logger.error("❌ Must use --confirm flag to reset database")
        return False

    logger.warning("⚠️  Resetting database...")

    try:
        reset_database(confirm=True)
        logger.info("✅ Database reset completed")
        return True

    except Exception as e:
        logger.error(f"❌ Database reset failed: {e}")
        return False


@script_main
def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Database migration management")
    subparsers = parser.add_subparsers(dest="command", help="Available commands")

    # Status command
    subparsers.add_parser("status", help="Show migration status")

    # Migrate command
    subparsers.add_parser("migrate", help="Run pending migrations")

    # Rollback command
    subparsers.add_parser("rollback", help="Rollback to previous migration")

    # Create command
    create_parser = subparsers.add_parser("create", help="Create a new migration")
    create_parser.add_argument("message", help="Migration message")

    # Reset command
    reset_parser = subparsers.add_parser("reset", help="Reset database")
    reset_parser.add_argument("--confirm", action="store_true", help="Confirm database reset")

    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        return False

    with ScriptContext(__name__) as logger:
        # Execute command
        success = False

        if args.command == "status":
            success = show_status(logger)
        elif args.command == "migrate":
            success = migrate(logger)
        elif args.command == "rollback":
            success = rollback(logger)
        elif args.command == "create":
            success = create_new_migration(args.message, logger)
        elif args.command == "reset":
            success = reset_db(args.confirm, logger)
        else:
            logger.error(f"❌ Unknown command: {args.command}")
            return False

        return success


if __name__ == "__main__":
    main()
