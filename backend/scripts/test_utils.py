#!/usr/bin/env python3
"""
Common Test Utilities

This module provides common utilities and setup functions for all test scripts
to eliminate code duplication and standardize testing patterns.
"""

import sys
import logging
from pathlib import Path
from typing import Tuple, Any

def setup_test_environment() -> Path:
    """
    Set up the test environment by adding backend to Python path.
    
    Returns:
        Path to the backend directory
    """
    backend_path = Path(__file__).parent.parent
    if str(backend_path) not in sys.path:
        sys.path.insert(0, str(backend_path))
    return backend_path


def get_common_imports() -> Tuple[Any, ...]:
    """
    Get common imports used across error and logging tests.
    
    Returns:
        Tuple of commonly used classes and functions
    """
    from app.core.logging import setup_logging, get_logger, LogContext
    from app.errors import (
        BusinessLMError,
        ConfigurationError,
        DatabaseError,
        EmbeddingError,
        ErrorCode,
        handle_database_error,
        handle_embedding_error
    )
    
    return (
        setup_logging, get_logger, LogContext,
        BusinessLMError, ConfigurationError, DatabaseError, EmbeddingError,
        ErrorCode, handle_database_error, handle_embedding_error
    )


def setup_clean_logging(log_level: str = "INFO") -> logging.Logger:
    """
    Set up clean logging configuration for tests.
    
    Args:
        log_level: Logging level to use
        
    Returns:
        Logger instance for the test
    """
    setup_logging, get_logger, _ = get_common_imports()[:3]
    setup_logging(log_level=log_level, force_reconfigure=True)
    return get_logger(__name__)


def suppress_error_logging() -> Tuple[logging.Logger, int]:
    """
    Temporarily suppress error logging for cleaner test output.
    
    Returns:
        Tuple of (error_logger, original_level) for restoration
    """
    error_logger = logging.getLogger("app.errors")
    original_level = error_logger.level
    error_logger.setLevel(logging.CRITICAL + 1)  # Suppress all logs
    return error_logger, original_level


def restore_error_logging(error_logger: logging.Logger, original_level: int) -> None:
    """
    Restore error logging to original level.
    
    Args:
        error_logger: The error logger instance
        original_level: Original logging level to restore
    """
    error_logger.setLevel(original_level)


def create_test_error(error_class: Any, message: str, error_code: Any, 
                     context: dict = None, auto_log: bool = False) -> Any:
    """
    Create a test error with standard parameters.
    
    Args:
        error_class: Error class to instantiate
        message: Error message
        error_code: Error code enum value
        context: Optional context dictionary
        auto_log: Whether to auto-log the error
        
    Returns:
        Error instance
    """
    return error_class(
        message=message,
        error_code=error_code,
        context=context or {},
        auto_log=auto_log
    )


def validate_error_serialization(error: Any, required_fields: list = None) -> bool:
    """
    Validate that an error can be properly serialized.
    
    Args:
        error: Error instance to validate
        required_fields: List of required fields in serialization
        
    Returns:
        True if serialization is valid, False otherwise
    """
    if required_fields is None:
        required_fields = ["error_code", "message", "context", "original_error"]
    
    try:
        error_dict = error.to_dict()
        return all(field in error_dict for field in required_fields)
    except Exception:
        return False


def run_test_suite(test_functions: list, logger: logging.Logger, 
                  suite_name: str = "Test Suite") -> bool:
    """
    Run a suite of test functions with consistent logging and error handling.
    
    Args:
        test_functions: List of test functions to run
        logger: Logger instance to use
        suite_name: Name of the test suite
        
    Returns:
        True if all tests passed, False otherwise
    """
    logger.info(f"🚀 Starting {suite_name}")
    logger.info("=" * 60)
    
    results = []
    for test_func in test_functions:
        try:
            test_func()
            results.append(True)
        except Exception as e:
            logger.error(f"❌ Test {test_func.__name__} failed: {e}", exc_info=True)
            results.append(False)
    
    logger.info("=" * 60)
    
    if all(results):
        logger.info(f"🎉 All {suite_name} tests completed successfully!")
        logger.info("✅ Error handling and logging system is working correctly")
        return True
    else:
        failed_count = len([r for r in results if not r])
        logger.error(f"❌ {failed_count}/{len(results)} tests failed")
        return False


class TestContext:
    """Context manager for test setup and cleanup."""
    
    def __init__(self, log_level: str = "INFO", suppress_errors: bool = False):
        """
        Initialize test context.
        
        Args:
            log_level: Logging level to use
            suppress_errors: Whether to suppress error logging
        """
        self.log_level = log_level
        self.suppress_errors = suppress_errors
        self.logger = None
        self.error_logger = None
        self.original_level = None
    
    def __enter__(self):
        """Enter test context."""
        # Set up environment
        setup_test_environment()
        
        # Set up logging
        self.logger = setup_clean_logging(self.log_level)
        
        # Suppress error logging if requested
        if self.suppress_errors:
            self.error_logger, self.original_level = suppress_error_logging()
        
        return self.logger
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Exit test context."""
        # Restore error logging if it was suppressed
        if self.suppress_errors and self.error_logger:
            restore_error_logging(self.error_logger, self.original_level)
