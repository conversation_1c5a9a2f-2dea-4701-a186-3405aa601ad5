#!/usr/bin/env python3
"""
Test Core Logging System (No External Dependencies)

This script tests the core logging infrastructure without requiring
external dependencies like Pydantic.
"""

import sys
import logging
import json
from pathlib import Path

# Add backend to path for imports
backend_path = Path(__file__).parent.parent
sys.path.insert(0, str(backend_path))

# Test the core logging components directly
from app.core.logging import (
    EnhancedFormatter, 
    StructuredJSONFormatter,
    LogContext,
    log_function_call,
    log_performance
)

from app.errors import (
    BusinessLMError,
    ErrorCode,
    ConfigurationError,
    DatabaseError
)


def test_enhanced_formatter():
    """Test the enhanced formatter."""
    print("🧪 Testing Enhanced Formatter")
    
    # Create a test logger with enhanced formatter
    logger = logging.getLogger("test_enhanced")
    logger.setLevel(logging.DEBUG)
    
    # Remove any existing handlers
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # Add console handler with enhanced formatter
    handler = logging.StreamHandler(sys.stdout)
    formatter = EnhancedFormatter(use_colors=True)
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    
    # Test different log levels
    logger.debug("This is a debug message")
    logger.info("This is an info message")
    logger.warning("This is a warning message")
    logger.error("This is an error message")
    logger.critical("This is a critical message")
    
    print("✅ Enhanced formatter test completed\n")


def test_json_formatter():
    """Test the JSON formatter."""
    print("🧪 Testing JSON Formatter")
    
    # Create a test logger with JSON formatter
    logger = logging.getLogger("test_json")
    logger.setLevel(logging.DEBUG)
    
    # Remove any existing handlers
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # Add console handler with JSON formatter
    handler = logging.StreamHandler(sys.stdout)
    formatter = StructuredJSONFormatter()
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    
    # Test different log levels
    logger.info("JSON formatted info message")
    logger.error("JSON formatted error message")
    
    # Test with exception
    try:
        raise ValueError("Test exception")
    except Exception:
        logger.error("JSON formatted error with exception", exc_info=True)
    
    print("\n✅ JSON formatter test completed\n")


def test_custom_errors():
    """Test custom error classes."""
    print("🧪 Testing Custom Error Classes")
    
    # Test BusinessLMError
    try:
        raise BusinessLMError(
            "Test business error",
            error_code=ErrorCode.UNKNOWN_ERROR,
            context={"test_key": "test_value"}
        )
    except BusinessLMError as e:
        print(f"✅ BusinessLMError: {e}")
        print(f"   Error dict: {e.to_dict()}")
    
    # Test ConfigurationError
    try:
        raise ConfigurationError(
            "Invalid configuration",
            error_code=ErrorCode.CONFIG_INVALID,
            context={"config_file": "test.env"}
        )
    except ConfigurationError as e:
        print(f"✅ ConfigurationError: {e}")
    
    # Test DatabaseError
    try:
        raise DatabaseError(
            "Database connection failed",
            error_code=ErrorCode.DATABASE_CONNECTION_FAILED
        )
    except DatabaseError as e:
        print(f"✅ DatabaseError: {e}")
    
    print("✅ Custom error test completed\n")


@log_function_call(logging.getLogger("test_decorator"), log_args=True, log_result=True)
def test_function(x: int, y: int) -> int:
    """Test function with logging decorator."""
    return x + y


@log_performance(logging.getLogger("test_performance"), threshold_seconds=0.05)
def slow_function():
    """Test function that should trigger performance warning."""
    import time
    time.sleep(0.1)  # Sleep longer than threshold
    return "completed"


def test_decorators():
    """Test logging decorators."""
    print("🧪 Testing Logging Decorators")
    
    # Set up logger for decorators
    logger = logging.getLogger("test_decorator")
    logger.setLevel(logging.DEBUG)
    
    if not logger.handlers:
        handler = logging.StreamHandler(sys.stdout)
        formatter = EnhancedFormatter(use_colors=False)
        handler.setFormatter(formatter)
        logger.addHandler(handler)
    
    # Test function call logging
    result = test_function(5, 3)
    print(f"Function result: {result}")
    
    # Test performance logging
    perf_logger = logging.getLogger("test_performance")
    perf_logger.setLevel(logging.DEBUG)
    
    if not perf_logger.handlers:
        handler = logging.StreamHandler(sys.stdout)
        formatter = EnhancedFormatter(use_colors=False)
        handler.setFormatter(formatter)
        perf_logger.addHandler(handler)
    
    result = slow_function()
    print(f"Performance function result: {result}")
    
    print("✅ Decorator test completed\n")


def test_error_codes():
    """Test error code enumeration."""
    print("🧪 Testing Error Codes")
    
    # Test that all error codes are accessible
    error_codes = [
        ErrorCode.CONFIG_MISSING,
        ErrorCode.DATABASE_CONNECTION_FAILED,
        ErrorCode.EMBEDDING_GENERATION_FAILED,
        ErrorCode.DOCUMENT_NOT_FOUND,
        ErrorCode.LLM_API_ERROR,
        ErrorCode.VALIDATION_ERROR,
        ErrorCode.TIMEOUT_ERROR,
        ErrorCode.UNKNOWN_ERROR
    ]
    
    for code in error_codes:
        print(f"✅ Error code: {code.value}")
    
    print("✅ Error code test completed\n")


def main():
    """Run all tests."""
    print("🚀 Starting Core Error Handling and Logging Tests")
    print("=" * 60)
    
    try:
        test_enhanced_formatter()
        test_json_formatter()
        test_custom_errors()
        test_decorators()
        test_error_codes()
        
        print("=" * 60)
        print("🎉 All core tests completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
