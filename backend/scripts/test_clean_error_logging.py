#!/usr/bin/env python3
"""
Clean Error Handling and Logging System Test

This script tests the error handling and logging infrastructure
with minimal noise and clear demonstration of functionality.
"""

import sys
import logging
from pathlib import Path

# Add backend to path for imports
backend_path = Path(__file__).parent.parent
sys.path.insert(0, str(backend_path))

from app.core.logging import setup_logging, get_logger, LogContext
from app.errors import (
    BusinessLMError, 
    ConfigurationError, 
    DatabaseError, 
    EmbeddingError,
    ErrorCode,
    handle_database_error,
    handle_embedding_error
)

# Set up logging cleanly - only once
setup_logging(log_level="INFO", force_reconfigure=True)
logger = get_logger(__name__)


def test_error_creation_control():
    """Test that we can control when errors are logged."""
    logger.info("🧪 Testing Error Creation Control")
    
    # Create error without logging (default behavior)
    silent_error = BusinessLMError(
        "This error is created silently",
        error_code=ErrorCode.VALIDATION_ERROR,
        context={"mode": "silent"}
    )
    logger.info(f"✅ Silent error created: {silent_error}")
    
    # Create error with explicit logging
    logger.info("   Now creating an error with auto-logging enabled...")
    try:
        raise ConfigurationError(
            "This error will be logged",
            error_code=ErrorCode.CONFIG_INVALID,
            context={"mode": "logged"},
            auto_log=True
        )
    except ConfigurationError as e:
        logger.info(f"✅ Logged error caught: {e}")
    
    logger.info("✅ Error creation control test completed\n")


def test_structured_logging():
    """Test structured logging with context."""
    logger.info("🧪 Testing Structured Logging")
    
    # Test context manager
    with LogContext(logger, service="test_service", request_id="req_123"):
        logger.info("Processing request with context")
        logger.warning("Warning with structured context")
    
    logger.info("Message without context (after context manager)")
    logger.info("✅ Structured logging test completed\n")


def test_error_serialization():
    """Test error serialization capabilities."""
    logger.info("🧪 Testing Error Serialization")
    
    # Create a comprehensive error
    error = BusinessLMError(
        "Comprehensive test error",
        error_code=ErrorCode.DOCUMENT_LOAD_FAILED,
        context={
            "file_path": "/path/to/document.pdf",
            "file_size": 1024,
            "attempt": 3
        },
        original_error=FileNotFoundError("File not found")
    )
    
    # Test string representation
    logger.info(f"String format: {error}")
    
    # Test dictionary serialization
    error_dict = error.to_dict()
    logger.info(f"Dict format: {error_dict}")
    
    # Verify serialization completeness
    required_fields = ["error_code", "message", "context", "original_error"]
    missing_fields = [field for field in required_fields if field not in error_dict]
    
    if not missing_fields:
        logger.info("✅ All required fields present in serialization")
    else:
        logger.error(f"❌ Missing fields: {missing_fields}")
    
    logger.info("✅ Error serialization test completed\n")


def test_decorator_functionality():
    """Test error handling decorators (without showing ERROR logs)."""
    logger.info("🧪 Testing Decorator Functionality")
    
    @handle_database_error
    def simulate_db_operation():
        """Simulate a database operation that fails."""
        raise ConnectionError("Database connection lost")
    
    @handle_embedding_error
    def simulate_embedding_operation():
        """Simulate an embedding operation that fails."""
        raise TimeoutError("Embedding service timeout")
    
    # Test database decorator (temporarily suppress ERROR logging)
    original_level = logging.getLogger("app.errors").level
    logging.getLogger("app.errors").setLevel(logging.CRITICAL)
    
    try:
        simulate_db_operation()
    except DatabaseError as e:
        logger.info(f"✅ Database decorator converted error: {e.error_code.value}")
    
    try:
        simulate_embedding_operation()
    except EmbeddingError as e:
        logger.info(f"✅ Embedding decorator converted error: {e.error_code.value}")
    
    # Restore original logging level
    logging.getLogger("app.errors").setLevel(original_level)
    
    logger.info("✅ Decorator functionality test completed\n")


def test_performance_monitoring():
    """Test performance monitoring capabilities."""
    logger.info("🧪 Testing Performance Monitoring")
    
    from app.core.logging import log_performance
    
    @log_performance(logger, threshold_seconds=0.05)
    def fast_operation():
        """Fast operation that shouldn't trigger warning."""
        import time
        time.sleep(0.01)
        return "fast_result"
    
    @log_performance(logger, threshold_seconds=0.05)
    def slow_operation():
        """Slow operation that should trigger warning."""
        import time
        time.sleep(0.1)
        return "slow_result"
    
    # Test fast operation (no warning expected)
    result1 = fast_operation()
    logger.info(f"Fast operation result: {result1}")
    
    # Test slow operation (warning expected)
    result2 = slow_operation()
    logger.info(f"Slow operation result: {result2}")
    
    logger.info("✅ Performance monitoring test completed\n")


def test_logging_levels():
    """Test different logging levels."""
    logger.info("🧪 Testing Logging Levels")
    
    # Test all levels
    logger.debug("Debug: Detailed diagnostic information")
    logger.info("Info: General operational information")
    logger.warning("Warning: Something unexpected but handled")
    logger.error("Error: Something went wrong but recoverable")
    logger.critical("Critical: Serious error requiring immediate attention")
    
    logger.info("✅ Logging levels test completed\n")


def main():
    """Run all clean tests."""
    logger.info("🚀 Starting Clean Error Handling and Logging Tests")
    logger.info("=" * 60)
    
    try:
        test_error_creation_control()
        test_structured_logging()
        test_error_serialization()
        test_decorator_functionality()
        test_performance_monitoring()
        test_logging_levels()
        
        logger.info("=" * 60)
        logger.info("🎉 All tests completed successfully!")
        logger.info("✅ Error handling and logging system is working correctly")
        return True
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}", exc_info=True)
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
