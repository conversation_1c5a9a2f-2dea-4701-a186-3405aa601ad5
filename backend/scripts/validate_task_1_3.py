#!/usr/bin/env python3
"""
Validate Task 1.3: Error Handling & Logging System

This script validates that Task 1.3 has been correctly implemented.
"""

import sys
from pathlib import Path

# Import common test utilities
from test_utils import setup_test_environment

# Set up test environment
backend_path = setup_test_environment()

def validate_error_classes():
    """Validate that all error classes are available."""
    print("🧪 Validating Error Classes...")

    try:
        from app.errors import (
            BusinessLMError,
            ConfigurationError,
            DatabaseError,
            EmbeddingError,
            DocumentError,
            LLMError,
            ValidationError,
            TimeoutError,
            ErrorCode
        )

        # Test error code enumeration
        assert hasattr(ErrorCode, 'CONFIG_MISSING')
        assert hasattr(ErrorCode, 'DATABASE_CONNECTION_FAILED')
        assert hasattr(ErrorCode, 'EMBEDDING_GENERATION_FAILED')
        assert hasattr(ErrorCode, 'DOCUMENT_NOT_FOUND')
        assert hasattr(ErrorCode, 'LLM_API_ERROR')
        assert hasattr(ErrorCode, 'VALIDATION_ERROR')
        assert hasattr(ErrorCode, 'TIMEOUT_ERROR')
        assert hasattr(ErrorCode, 'UNKNOWN_ERROR')

        # Test error class instantiation
        error = BusinessLMError("Test error", ErrorCode.UNKNOWN_ERROR)
        assert error.error_code == ErrorCode.UNKNOWN_ERROR
        assert error.message == "Test error"

        print("✅ Error classes validation passed")
        return True

    except Exception as e:
        print(f"❌ Error classes validation failed: {e}")
        return False


def validate_error_decorators():
    """Validate that error handling decorators are available."""
    print("🧪 Validating Error Decorators...")

    try:
        from app.errors import (
            handle_database_error,
            handle_embedding_error,
            handle_llm_error,
            log_and_reraise
        )

        # Test that decorators are callable
        assert callable(handle_database_error)
        assert callable(handle_embedding_error)
        assert callable(handle_llm_error)
        assert callable(log_and_reraise)

        print("✅ Error decorators validation passed")
        return True

    except Exception as e:
        print(f"❌ Error decorators validation failed: {e}")
        return False


def validate_logging_system():
    """Validate that the logging system is available."""
    print("🧪 Validating Logging System...")

    try:
        from app.core.logging import (
            setup_logging,
            get_logger,
            get_structured_logger,
            LogContext,
            log_function_call,
            log_performance,
            EnhancedFormatter,
            StructuredJSONFormatter
        )

        # Test logger creation
        logger = get_logger("test")
        assert logger is not None

        # Test formatters
        enhanced_formatter = EnhancedFormatter()
        json_formatter = StructuredJSONFormatter()
        assert enhanced_formatter is not None
        assert json_formatter is not None

        print("✅ Logging system validation passed")
        return True

    except Exception as e:
        print(f"❌ Logging system validation failed: {e}")
        return False


def validate_imports():
    """Validate that components can be imported from main packages."""
    print("🧪 Validating Package Imports...")

    try:
        # Test imports from app package
        from app import (
            BusinessLMError,
            ConfigurationError,
            DatabaseError,
            ErrorCode
        )

        # Test imports from app.core package
        from app.core import (
            setup_logging,
            get_logger,
            LogContext
        )

        print("✅ Package imports validation passed")
        return True

    except Exception as e:
        print(f"❌ Package imports validation failed: {e}")
        return False


def validate_file_structure():
    """Validate that all required files exist."""
    print("🧪 Validating File Structure...")

    required_files = [
        "app/errors.py",
        "app/core/logging.py",
        "app/__init__.py",
        "app/core/__init__.py"
    ]

    missing_files = []
    for file_path in required_files:
        full_path = backend_path / file_path
        if not full_path.exists():
            missing_files.append(file_path)

    if missing_files:
        print(f"❌ Missing files: {missing_files}")
        return False

    print("✅ File structure validation passed")
    return True


def main():
    """Run all validations."""
    print("🚀 Validating Task 1.3: Error Handling & Logging System")
    print("=" * 60)

    validations = [
        validate_file_structure,
        validate_error_classes,
        validate_error_decorators,
        validate_logging_system,
        validate_imports
    ]

    results = []
    for validation in validations:
        try:
            result = validation()
            results.append(result)
        except Exception as e:
            print(f"❌ Validation failed with exception: {e}")
            results.append(False)

    print("=" * 60)

    if all(results):
        print("🎉 Task 1.3 validation completed successfully!")
        print("✅ Error Handling & Logging System is fully implemented")
        return True
    else:
        print("❌ Task 1.3 validation failed")
        failed_count = len([r for r in results if not r])
        print(f"   {failed_count}/{len(results)} validations failed")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
