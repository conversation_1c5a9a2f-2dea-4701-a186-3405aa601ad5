#!/usr/bin/env python3
"""
Test Error Handling and Logging System

This script tests the error handling and logging infrastructure to ensure
everything is working correctly.
"""

import sys
import logging
from pathlib import Path

# Add backend to path for imports
backend_path = Path(__file__).parent.parent
sys.path.insert(0, str(backend_path))

from app.core.logging import setup_logging, get_logger, LogContext, log_function_call, log_performance
from app.errors import (
    BusinessLMError, 
    ConfigurationError, 
    DatabaseError, 
    EmbeddingError,
    ErrorCode,
    handle_database_error,
    handle_embedding_error
)

# Set up logging
setup_logging(log_level="DEBUG")
logger = get_logger(__name__)


def test_basic_logging():
    """Test basic logging functionality."""
    logger.info("🧪 Testing basic logging functionality")
    
    logger.debug("This is a debug message")
    logger.info("This is an info message")
    logger.warning("This is a warning message")
    logger.error("This is an error message")
    logger.critical("This is a critical message")
    
    logger.info("✅ Basic logging test completed")


def test_structured_logging():
    """Test structured logging with context."""
    logger.info("🧪 Testing structured logging with context")
    
    # Test log context manager
    with LogContext(logger, user_id="test_user", operation="test_operation"):
        logger.info("This message should include context")
        logger.warning("This warning should also include context")
    
    logger.info("✅ Structured logging test completed")


def test_custom_errors():
    """Test custom error classes."""
    logger.info("🧪 Testing custom error classes")
    
    try:
        # Test BusinessLMError
        raise BusinessLMError(
            "Test error message",
            error_code=ErrorCode.UNKNOWN_ERROR,
            context={"test_key": "test_value"}
        )
    except BusinessLMError as e:
        logger.info(f"Caught BusinessLMError: {e}")
        logger.info(f"Error dict: {e.to_dict()}")
    
    try:
        # Test ConfigurationError
        raise ConfigurationError(
            "Configuration is invalid",
            error_code=ErrorCode.CONFIG_INVALID,
            context={"config_file": "test.env"}
        )
    except ConfigurationError as e:
        logger.info(f"Caught ConfigurationError: {e}")
    
    try:
        # Test DatabaseError
        raise DatabaseError(
            "Database connection failed",
            error_code=ErrorCode.DATABASE_CONNECTION_FAILED,
            context={"database_url": "postgresql://localhost:5432/test"}
        )
    except DatabaseError as e:
        logger.info(f"Caught DatabaseError: {e}")
    
    logger.info("✅ Custom error test completed")


@handle_database_error
def test_database_operation():
    """Test function that simulates a database operation."""
    # Simulate a database error
    raise Exception("Simulated database connection error")


@handle_embedding_error  
def test_embedding_operation():
    """Test function that simulates an embedding operation."""
    # Simulate an embedding error
    raise Exception("Simulated embedding generation error")


@log_function_call(logger, log_args=True, log_result=True)
def test_logged_function(x: int, y: int) -> int:
    """Test function with logging decorator."""
    return x + y


@log_performance(logger, threshold_seconds=0.1)
def test_performance_function():
    """Test function with performance logging."""
    import time
    time.sleep(0.2)  # Sleep longer than threshold
    return "completed"


def test_error_decorators():
    """Test error handling decorators."""
    logger.info("🧪 Testing error handling decorators")
    
    try:
        test_database_operation()
    except DatabaseError as e:
        logger.info(f"Database decorator worked: {e}")
    
    try:
        test_embedding_operation()
    except EmbeddingError as e:
        logger.info(f"Embedding decorator worked: {e}")
    
    logger.info("✅ Error decorator test completed")


def test_logging_decorators():
    """Test logging decorators."""
    logger.info("🧪 Testing logging decorators")
    
    # Test function call logging
    result = test_logged_function(5, 3)
    logger.info(f"Function result: {result}")
    
    # Test performance logging
    result = test_performance_function()
    logger.info(f"Performance function result: {result}")
    
    logger.info("✅ Logging decorator test completed")


def test_exception_logging():
    """Test exception logging with traceback."""
    logger.info("🧪 Testing exception logging")
    
    try:
        # Create a nested exception
        def inner_function():
            raise ValueError("Inner exception")
        
        def outer_function():
            try:
                inner_function()
            except ValueError as e:
                raise RuntimeError("Outer exception") from e
        
        outer_function()
        
    except Exception as e:
        logger.error("Exception with traceback", exc_info=True)
    
    logger.info("✅ Exception logging test completed")


def main():
    """Run all tests."""
    logger.info("🚀 Starting Error Handling and Logging System Tests")
    logger.info("=" * 60)
    
    try:
        test_basic_logging()
        test_structured_logging()
        test_custom_errors()
        test_error_decorators()
        test_logging_decorators()
        test_exception_logging()
        
        logger.info("=" * 60)
        logger.info("🎉 All tests completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}", exc_info=True)
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
