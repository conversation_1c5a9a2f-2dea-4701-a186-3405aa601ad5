#!/usr/bin/env python3
"""
Comprehensive Error Handling and Logging System Test

This script provides comprehensive testing of the error handling and logging
infrastructure with multiple test modes and clean output options.

This is the consolidated test file that replaces multiple redundant test scripts.
"""

import sys
import logging
from pathlib import Path

# Import common test utilities
from test_utils import (
    setup_test_environment,
    get_common_imports,
    setup_clean_logging,
    suppress_error_logging,
    restore_error_logging,
    create_test_error,
    validate_error_serialization,
    run_test_suite,
    TestContext
)

# Set up test environment
setup_test_environment()

# Get common imports
(setup_logging, get_logger, LogContext,
 BusinessLMError, ConfigurationError, DatabaseError, EmbeddingError,
 ErrorCode, handle_database_error, handle_embedding_error) = get_common_imports()

# Import additional logging utilities
from app.core.logging import log_function_call, log_performance


# Test mode configuration
TEST_MODE = "comprehensive"  # Options: "comprehensive", "clean", "ultra_clean"

def test_basic_logging(logger):
    """Test basic logging functionality."""
    logger.info("🧪 Testing basic logging functionality")

    if TEST_MODE == "comprehensive":
        logger.debug("This is a debug message")
        logger.info("This is an info message")
        logger.warning("This is a warning message")
        logger.error("This is an error message")
        logger.critical("This is a critical message")
    else:
        logger.debug("Debug: Detailed diagnostic information")
        logger.info("Info: General operational information")
        if TEST_MODE == "comprehensive":
            logger.warning("Warning: Something unexpected but handled")
            logger.error("Error: Something went wrong but recoverable")
            logger.critical("Critical: Serious error requiring immediate attention")

    logger.info("✅ Basic logging test completed")


def test_structured_logging(logger):
    """Test structured logging with context."""
    logger.info("🧪 Testing structured logging with context")

    # Test log context manager
    with LogContext(logger, user_id="test_user", operation="test_operation"):
        logger.info("Processing request with structured context")
        if TEST_MODE == "comprehensive":
            logger.warning("Warning with structured context")

    logger.info("Message without context (after context manager)")
    logger.info("✅ Structured logging test completed")


def test_custom_errors(logger):
    """Test custom error classes with different modes."""
    logger.info("🧪 Testing custom error classes")

    # Test silent error creation (default behavior)
    silent_error = create_test_error(
        BusinessLMError,
        "Silent error for testing",
        ErrorCode.VALIDATION_ERROR,
        {"mode": "silent"}
    )
    logger.info(f"✅ Created silent error: {silent_error.error_code.value}")

    # Test error serialization
    if validate_error_serialization(silent_error):
        logger.info("✅ Error serialization validation passed")
    else:
        logger.error("❌ Error serialization validation failed")

    if TEST_MODE == "comprehensive":
        # Test with auto-logging enabled
        try:
            raise create_test_error(
                ConfigurationError,
                "Configuration validation failed",
                ErrorCode.CONFIG_INVALID,
                {"config_file": "test.env"},
                auto_log=True
            )
        except ConfigurationError as e:
            logger.info(f"✅ Auto-logged error caught: {e.error_code.value}")

    # Test different error types
    error_types = [
        (DatabaseError, ErrorCode.DATABASE_CONNECTION_FAILED, "Database connection failed"),
        (EmbeddingError, ErrorCode.EMBEDDING_GENERATION_FAILED, "Embedding generation failed"),
        (BusinessLMError, ErrorCode.DOCUMENT_LOAD_FAILED, "Document processing failed")
    ]

    for error_class, error_code, message in error_types:
        error = create_test_error(error_class, message, error_code)
        logger.info(f"✅ Created {error_class.__name__}: {error_code.value}")

    logger.info("✅ Custom error test completed")


@handle_database_error
def test_database_operation():
    """Test function that simulates a database operation."""
    # Simulate a database error
    raise Exception("Simulated database connection error")


@handle_embedding_error
def test_embedding_operation():
    """Test function that simulates an embedding operation."""
    # Simulate an embedding error
    raise Exception("Simulated embedding generation error")


def test_logged_function(x: int, y: int) -> int:
    """Test function with logging decorator."""
    return x + y


def test_performance_function():
    """Test function with performance logging."""
    import time
    time.sleep(0.2)  # Sleep longer than threshold
    return "completed"


def test_error_decorators(logger):
    """Test error handling decorators."""
    logger.info("🧪 Testing error handling decorators")

    # Suppress error logging for cleaner output in clean modes
    if TEST_MODE in ["clean", "ultra_clean"]:
        error_logger, original_level = suppress_error_logging()

    try:
        try:
            test_database_operation()
        except DatabaseError as e:
            logger.info(f"✅ Database decorator: {e.error_code.value}")

        try:
            test_embedding_operation()
        except EmbeddingError as e:
            logger.info(f"✅ Embedding decorator: {e.error_code.value}")

    finally:
        # Restore error logging if it was suppressed
        if TEST_MODE in ["clean", "ultra_clean"]:
            restore_error_logging(error_logger, original_level)

    logger.info("✅ Error decorator test completed")


def test_logging_decorators(logger):
    """Test logging decorators."""
    logger.info("🧪 Testing logging decorators")

    # Test function call logging with dynamic decorator
    logged_function = log_function_call(logger, log_args=True, log_result=True)(test_logged_function)
    result = logged_function(5, 3)
    logger.info(f"✅ Function result: {result}")

    # Test performance logging with dynamic decorator
    perf_function = log_performance(logger, threshold_seconds=0.1)(test_performance_function)
    result = perf_function()
    logger.info(f"✅ Performance function result: {result}")

    logger.info("✅ Logging decorator test completed")


def test_exception_logging():
    """Test exception logging with traceback."""
    logger.info("🧪 Testing exception logging")

    try:
        # Create a nested exception
        def inner_function():
            raise ValueError("Inner exception")

        def outer_function():
            try:
                inner_function()
            except ValueError as e:
                raise RuntimeError("Outer exception") from e

        outer_function()

    except Exception as e:
        logger.error("Exception with traceback", exc_info=True)

    logger.info("✅ Exception logging test completed")


def test_exception_logging(logger):
    """Test exception logging with traceback."""
    if TEST_MODE != "comprehensive":
        logger.info("🧪 Testing exception logging (skipped in clean mode)")
        logger.info("✅ Exception logging test completed")
        return

    logger.info("🧪 Testing exception logging")

    try:
        # Create a nested exception
        def inner_function():
            raise ValueError("Inner exception")

        def outer_function():
            try:
                inner_function()
            except ValueError as e:
                raise RuntimeError("Outer exception") from e

        outer_function()

    except Exception as e:
        logger.error("Exception with traceback", exc_info=True)

    logger.info("✅ Exception logging test completed")


def run_comprehensive_tests():
    """Run comprehensive tests with all features."""
    global TEST_MODE
    TEST_MODE = "comprehensive"

    with TestContext(log_level="DEBUG") as logger:
        test_functions = [
            lambda: test_basic_logging(logger),
            lambda: test_structured_logging(logger),
            lambda: test_custom_errors(logger),
            lambda: test_error_decorators(logger),
            lambda: test_logging_decorators(logger),
            lambda: test_exception_logging(logger)
        ]

        return run_test_suite(test_functions, logger, "Comprehensive Error Handling and Logging Tests")


def run_clean_tests():
    """Run clean tests with minimal ERROR/WARNING output."""
    global TEST_MODE
    TEST_MODE = "clean"

    with TestContext(log_level="INFO") as logger:
        test_functions = [
            lambda: test_basic_logging(logger),
            lambda: test_structured_logging(logger),
            lambda: test_custom_errors(logger),
            lambda: test_error_decorators(logger),
            lambda: test_logging_decorators(logger)
        ]

        return run_test_suite(test_functions, logger, "Clean Error Handling and Logging Tests")


def run_ultra_clean_tests():
    """Run ultra-clean tests with no ERROR/WARNING output."""
    global TEST_MODE
    TEST_MODE = "ultra_clean"

    with TestContext(log_level="INFO", suppress_errors=True) as logger:
        test_functions = [
            lambda: test_basic_logging(logger),
            lambda: test_structured_logging(logger),
            lambda: test_custom_errors(logger),
            lambda: test_error_decorators(logger)
        ]

        return run_test_suite(test_functions, logger, "Ultra-Clean Error Handling and Logging Tests")


def main():
    """Run tests based on command line arguments or default mode."""
    import argparse

    parser = argparse.ArgumentParser(description="Test Error Handling and Logging System")
    parser.add_argument(
        "--mode",
        choices=["comprehensive", "clean", "ultra_clean"],
        default="comprehensive",
        help="Test mode to run (default: comprehensive)"
    )

    args = parser.parse_args()

    if args.mode == "comprehensive":
        return run_comprehensive_tests()
    elif args.mode == "clean":
        return run_clean_tests()
    elif args.mode == "ultra_clean":
        return run_ultra_clean_tests()
    else:
        print(f"Unknown mode: {args.mode}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
