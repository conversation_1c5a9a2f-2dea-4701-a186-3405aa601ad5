"""
RAG Utilities

This module provides utility functions for the RAG system.
"""

import logging
from typing import Dict, Any, Optional

from backend.app.config import get_settings
from .embeddings import get_embedding_model, EmbeddingModel
from .vector_store import get_vector_store, VectorStore

# Set up logging
logger = logging.getLogger(__name__)

class RAGConfig:
    """Configuration container for RAG components."""

    def __init__(self, **kwargs):
        """
        Initialize RAG configuration.

        Args:
            **kwargs: Configuration options
        """
        # Load from settings
        settings = get_settings()

        # Embedding configuration
        self.embedding_provider = kwargs.get("embedding_provider", settings.DEFAULT_EMBEDDING_MODEL)
        self.embedding_model = kwargs.get("embedding_model", settings.EMBEDDING_MODEL)
        self.embedding_dimension = kwargs.get("embedding_dimension", settings.VECTOR_DIMENSION)

        # Vector store configuration
        self.vector_store_type = kwargs.get("vector_store_type", settings.VECTOR_STORE_TYPE)
        self.vector_store_path = kwargs.get("vector_store_path", settings.VECTOR_STORE_PATH)

        # Retriever configuration
        self.retriever_type = kwargs.get("retriever_type", "hybrid")
        self.vector_weight = kwargs.get("vector_weight", 0.7)
        self.keyword_weight = kwargs.get("keyword_weight", 0.3)
        self.use_reranking = kwargs.get("use_reranking", False)

        # Context window configuration
        self.max_tokens = kwargs.get("max_tokens", 4000)
        self.token_buffer = kwargs.get("token_buffer", 1000)

        # Timeout configuration
        self.embedding_timeout = kwargs.get("embedding_timeout", 5)
        self.vector_search_timeout = kwargs.get("vector_search_timeout", 10)
        self.keyword_search_timeout = kwargs.get("keyword_search_timeout", 8)
        self.hybrid_search_timeout = kwargs.get("hybrid_search_timeout", 12)

        # Additional options
        self.additional_options = kwargs

async def initialize_embedding_model(config: Optional[RAGConfig] = None) -> EmbeddingModel:
    """
    Initialize an embedding model based on configuration.

    Args:
        config: RAG configuration

    Returns:
        Initialized embedding model
    """
    if config is None:
        config = RAGConfig()

    logger.info(f"Initializing embedding model: {config.embedding_provider}/{config.embedding_model}")

    return get_embedding_model(
        provider=config.embedding_provider,
        model_name=config.embedding_model,
        dimensions=config.embedding_dimension,
        **config.additional_options
    )

async def initialize_knowledge_base_service(
    embedding_model=None,
    vector_store=None,
    config: Optional[RAGConfig] = None
):
    """
    Initialize a knowledge base service.

    Args:
        embedding_model: Optional embedding model
        vector_store: Optional vector store
        config: RAG configuration

    Returns:
        Initialized knowledge base service
    """
    if config is None:
        config = RAGConfig()

    # Import here to avoid circular imports
    from .knowledge_base import KnowledgeBaseService

    # Initialize embedding model if not provided
    if embedding_model is None:
        embedding_model = await initialize_embedding_model(config)

    # Initialize vector store if not provided
    if vector_store is None:
        vector_store = await get_vector_store(
            store_type=config.vector_store_type,
            dimension=embedding_model.dimension,
            path=config.vector_store_path
        )

    logger.info("Initializing knowledge base service")

    return KnowledgeBaseService(
        embedding_model=embedding_model,
        vector_store=vector_store,
        vector_weight=config.vector_weight,
        keyword_weight=config.keyword_weight,
        use_reranking=config.use_reranking
    )

# Mock embedding model for testing
class EnhancedMockEmbeddingModel(EmbeddingModel):
    """Enhanced mock embedding model for testing."""

    def __init__(self, dimension: int = 1536):
        """Initialize mock embedding model."""
        self._dimension = dimension

    async def embed_query(self, text: str) -> list:
        """Generate mock query embedding."""
        import random
        return [random.random() for _ in range(self._dimension)]

    async def embed_documents(self, texts: list) -> list:
        """Generate mock document embeddings."""
        import random
        return [[random.random() for _ in range(self._dimension)] for _ in texts]

    @property
    def dimension(self) -> int:
        """Get embedding dimension."""
        return self._dimension