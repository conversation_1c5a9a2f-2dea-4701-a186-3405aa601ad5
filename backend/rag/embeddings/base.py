"""
Embedding Model Base Class

This module defines the abstract base class for embedding models.
"""

import logging
from abc import ABC, abstractmethod
from typing import List, Optional

# Set up logging
logger = logging.getLogger(__name__)


class EmbeddingModel(ABC):
    """
    Abstract base class for embedding models.

    WHAT IS THIS?
    -----------
    This is an abstract class that defines what any embedding model must be able to do.
    Think of it like a blueprint or template that all specific embedding models
    (like HuggingFaceEmbedding or OpenAIEmbedding) must follow.

    It's similar to how both a bicycle and a car must implement the concept of
    "transportation" - they do it differently, but they both need to get you
    from point A to point B.

    WHY IS THIS USEFUL?
    -----------------
    1. Consistency: All embedding models work the same way from the outside
    2. Interchangeability: You can swap one model for another without changing your code
    3. Extensibility: Easy to add new embedding models in the future

    WHAT MUST ALL EMBEDDING MODELS DO?
    -------------------------------
    Any embedding model must implement these three capabilities:

    1. embed_query: Convert a single question/query into numbers
       - Optimized for search queries
       - Example: "What is RAG?" → [0.1, 0.2, 0.3, ...]

    2. embed_documents: Convert multiple documents into numbers efficiently
       - Handles batches of text for better performance
       - Example: ["Doc1", "Doc2"] → [[0.1, 0.2, ...], [0.3, 0.4, ...]]

    3. dimension: Tell us how many numbers are in each embedding
       - This is a "property" (accessed like an attribute, not a method)
       - Example: model.dimension → 768

    WHY ARE THE METHODS ASYNC?
    -----------------------
    All methods use "async" which means they can pause without blocking other code.
    This is important because:

    1. API calls (like OpenAI) might take time to respond
    2. Local models might need to process large batches of text
    3. Your application can do other things while waiting for embeddings

    It's like being able to check your phone while waiting for coffee to brew,
    instead of just standing there doing nothing.

    HOW TO USE THIS CLASS?
    -------------------
    You don't use this class directly! It's just a template.
    Instead, you use concrete implementations like:

    - HuggingFaceEmbedding (local processing)
    - OpenAIEmbedding (API-based)
    """

    @abstractmethod
    async def embed_query(self, text: str) -> List[float]:
        """
        Convert a question or search query into a list of numbers.

        WHAT DOES THIS DO?
        ----------------
        This method takes a text string (like a question) and turns it into a list
        of numbers (an embedding) that captures the meaning of the text.

        Think of it like translating English to "number-language" that computers
        can understand and compare.

        WHY IS THIS USEFUL?
        -----------------
        When you want to find information related to a question, you can:
        1. Convert the question to numbers using this method
        2. Compare those numbers to the numbers of stored documents
        3. Find documents with the most similar numbers
        4. Return those documents as relevant information

        PARAMETERS:
        ----------
        text (str): The question or search query to convert to numbers
                    Example: "What is machine learning?"

        RETURNS:
        -------
        List[float]: A list of decimal numbers representing the meaning
                     Example: [0.123, -0.456, 0.789, ...]

                     These numbers typically have hundreds of dimensions
                     (768 for HuggingFace e5-base, 1024 for OpenAI embedding-3-small)

        IMPORTANT NOTES:
        --------------
        1. This is an "async" method, so you must use "await" when calling it:
           embedding = await model.embed_query("my question")

        2. Different models may produce different numbers for the same text

        3. This method is specifically optimized for questions/queries, while
           embed_documents() is optimized for document content
        """
        pass

    @abstractmethod
    async def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """
        Convert multiple documents into lists of numbers.

        WHAT DOES THIS DO?
        ----------------
        This method takes a list of text strings (like documents or paragraphs)
        and turns each one into a list of numbers (embeddings) that capture
        the meaning of each text.

        Think of it like translating a stack of documents from English to
        "number-language" that computers can understand and compare.

        WHY IS THIS USEFUL?
        -----------------
        When you want to build a searchable knowledge base, you can:
        1. Convert all your documents to numbers using this method
        2. Store these numbers in a vector database
        3. Later, when someone asks a question, you can convert their question
           to numbers and find the most similar document embeddings

        PARAMETERS:
        ----------
        texts (List[str]): A list of text strings to convert to numbers
                          Example: ["Document about AI", "Document about ML", ...]

        RETURNS:
        -------
        List[List[float]]: A list of embedding vectors, one for each input text
                          Example: [[0.1, 0.2, ...], [0.3, 0.4, ...], ...]

                          Each inner list represents one document's meaning

        IMPORTANT NOTES:
        --------------
        1. This is an "async" method, so you must use "await" when calling it:
           embeddings = await model.embed_documents(["doc1", "doc2"])

        2. This method is optimized for batching multiple documents at once,
           which is usually more efficient than calling embed_query() many times

        3. Some models handle documents differently than queries, which is why
           we have separate methods for each use case
        """
        pass

    @property
    def dimension(self) -> int:
        """
        Get the dimension (number of values) in each embedding vector.

        WHAT IS THIS?
        -----------
        This property tells you how many numbers are in each embedding vector
        produced by this model.

        Think of it like knowing how many pixels are in an image - it's a
        fundamental characteristic of the data format.

        WHY IS THIS IMPORTANT?
        -------------------
        1. Vector stores need to know the dimension to store embeddings properly
        2. Similarity calculations need vectors of the same dimension
        3. Different models produce different sized embeddings:
           - HuggingFace e5-base: 768 dimensions
           - OpenAI text-embedding-3-small: 1024 dimensions (default)
           - OpenAI text-embedding-3-large: 1536 dimensions

        HOW TO USE IT:
        ------------
        Since this is a property (not a method), you access it like this:

        ```python
        model = HuggingFaceEmbedding()
        dim = model.dimension  # Notice: no parentheses!
        print(f"This model produces embeddings with {dim} dimensions")
        ```

        RETURNS:
        -------
        int: The number of dimensions (numbers) in each embedding vector
             Example: 768
        """
        raise NotImplementedError("Subclasses must implement the dimension property")