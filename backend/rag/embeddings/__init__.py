"""
Embedding Models Package

This package provides embedding models for converting text to vector representations.
"""

from .base import EmbeddingModel
from .huggingface import Hugging<PERSON>aceEmbedding
from .openai import OpenAIEmbedding
from .factory import EmbeddingFactory, get_embedding_model

# Check for available dependencies
try:
    import sentence_transformers
    HUGGINGFACE_AVAILABLE = True
except ImportError:
    HUGGINGFACE_AVAILABLE = False

try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

# Define the list of exported symbols
__all__ = [
    "EmbeddingModel",
    "HuggingFaceEmbedding",
    "OpenAIEmbedding",
    "EmbeddingFactory",
    "get_embedding_model",
    "HUGGINGFACE_AVAILABLE",
    "OPENAI_AVAILABLE"
]