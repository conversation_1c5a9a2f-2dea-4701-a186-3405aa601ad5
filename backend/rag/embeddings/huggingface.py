"""
HuggingFace Embedding Model

This module implements the HuggingFaceEmbedding class for local embedding generation.
"""

import asyncio
import logging
from typing import List, Optional

from .base import EmbeddingModel

# Set up logging
logger = logging.getLogger(__name__)


class HuggingFaceEmbedding(EmbeddingModel):
    """
    Local embedding model that runs on your own computer using HuggingFace models.

    WHAT IS THIS?
    -----------
    This is a concrete implementation of the EmbeddingModel that uses HuggingFace's
    sentence-transformers library to generate embeddings locally on your machine.

    Think of it like having a translation tool installed directly on your computer
    instead of using an online translation service.

    ADVANTAGES:
    ---------
    1. Free to use - no API costs
    2. Works offline - no internet connection needed
    3. Privacy - your data never leaves your computer
    4. Customizable - many different models to choose from

    DISADVANTAGES:
    -----------
    1. Requires more computer resources (CPU/RAM/GPU)
    2. Initial download of models can be large (1-2GB)
    3. May be slower than cloud-based alternatives
    4. Requires installing additional Python packages

    POPULAR MODELS:
    ------------
    - "intfloat/e5-base-v2" (default): Good balance of quality and speed (768 dimensions)
    - "intfloat/e5-large-v2": Higher quality but slower and larger (1024 dimensions)
    - "hkunlp/instructor-xl": Instruction-tuned model that follows specific prompts
    """

    def __init__(
        self,
        model_name: str = "intfloat/e5-base-v2",
        device: str = "cpu",
        normalize_embeddings: bool = True,
        **kwargs  # This allows for additional options to be passed to the model
    ):
        """
        Set up a new HuggingFace embedding model.

        WHAT DOES THIS DO?
        ----------------
        This method prepares a HuggingFace embedding model for use by:
        1. Loading the specified model from HuggingFace's model hub
        2. Setting up configuration options like device and normalization
        3. Detecting if it's an "instructor" model that needs special prompting
        4. Determining the embedding dimension for the selected model

        PARAMETERS:
        ----------
        model_name (str): Which pre-trained model to use
            - Default: "intfloat/e5-base-v2" (good balance of quality and speed)
            - Other options: "intfloat/e5-large-v2", "hkunlp/instructor-xl"

        device (str): Which hardware to run the model on
            - "cpu": Use regular processor (slower but always works)
            - "cuda": Use NVIDIA GPU (much faster if available)
            - "mps": Use Apple M1/M2 GPU (faster on Mac)
            - Default: "cpu"

        normalize_embeddings (bool): Whether to make all vectors unit length
            - True: All vectors will have length 1.0 (better for similarity)
            - False: Keep raw vector values (may have varying magnitudes)
            - Default: True (recommended for most cases)

        **kwargs: Additional options to pass to the SentenceTransformer model
            - cache_folder: Where to store downloaded models
            - show_progress_bar: Whether to show download progress

        TECHNICAL DETAILS:
        ---------------
        - The model is loaded when this method is called (not when embedding)
        - For instructor models, we detect "instructor" in the name and use special prompts
        - The dimension is determined by asking the model for its embedding size
        - We use logging to record which model was initialized
        """
        try:
            from sentence_transformers import SentenceTransformer
            self.model = SentenceTransformer(model_name, device=device)
            self.normalize = normalize_embeddings
            self._dimension = self.model.get_sentence_embedding_dimension()
            self.model_name = model_name
            self.is_instructor = "instructor" in model_name.lower()
            logger.info(f"Initialized HuggingFaceEmbedding with model {model_name}")
        except ImportError:
            raise ImportError(
                "sentence-transformers package is required. "
                "Install with 'pip install sentence-transformers'"
            )

    async def embed_query(self, text: str) -> List[float]:
        """
        Convert a query text to a vector representation.

        Args:
            text: The query text to embed

        Returns:
            A list of floats representing the embedding vector
        """
        # Format text for instructor models
        if self.is_instructor:
            text = f"Represent this sentence for retrieval: {text}"

        # Use asyncio to run the embedding in a thread pool
        embedding = await asyncio.to_thread(
            self.model.encode,
            text,
            normalize_embeddings=self.normalize
        )

        return embedding.tolist()

    async def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """
        Convert a list of document texts to vector representations.

        Args:
            texts: List of document texts to embed

        Returns:
            A list of embedding vectors, one for each input text
        """
        # Format texts for instructor models
        if self.is_instructor:
            texts = [f"Represent this document for retrieval: {text}" for text in texts]

        # Use asyncio to run the embedding in a thread pool
        embeddings = await asyncio.to_thread(
            self.model.encode,
            texts,
            normalize_embeddings=self.normalize
        )

        return embeddings.tolist()

    @property
    def dimension(self) -> int:
        """
        Get the dimension of the embedding vectors.

        Returns:
            The number of dimensions in each embedding vector
        """
        return self._dimension