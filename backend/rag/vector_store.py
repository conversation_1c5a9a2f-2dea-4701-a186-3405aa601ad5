"""
Vector Store Implementation

This module provides a vector store implementation for PostgreSQL with pgvector.
The vector store is responsible for storing and retrieving embeddings.
"""

import asyncio
import json
import logging
import uuid
from typing import Dict, List, Any, Optional, Tuple, Union

import sqlalchemy as sa
from sqlalchemy.sql import text

from backend.app.core.db.database import get_db_context
from backend.app.config import get_settings

logger = logging.getLogger(__name__)

class VectorStore:
    """Base class for vector stores."""

    async def add_embeddings(
        self,
        embeddings: List[List[float]],
        texts: List[str],
        metadatas: Optional[List[Dict[str, Any]]] = None,
        ids: Optional[List[str]] = None
    ) -> List[str]:
        """
        Add embeddings to the vector store.

        Args:
            embeddings: List of embedding vectors
            texts: List of text strings corresponding to the embeddings
            metadatas: Optional list of metadata dictionaries
            ids: Optional list of IDs for the embeddings

        Returns:
            List of IDs for the added embeddings
        """
        raise NotImplementedError("Subclasses must implement add_embeddings")

    async def search(
        self,
        query_embedding: List[float],
        limit: int = 5,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        Search for similar embeddings.

        Args:
            query_embedding: The query embedding vector
            limit: Maximum number of results to return
            filters: Optional metadata filters

        Returns:
            List of dictionaries containing:
            - id: The embedding ID
            - text: The text string
            - metadata: The metadata dictionary
            - score: The similarity score
        """
        raise NotImplementedError("Subclasses must implement search")

    async def delete(self, ids: List[str]) -> None:
        """
        Delete embeddings by ID.

        Args:
            ids: List of embedding IDs to delete
        """
        raise NotImplementedError("Subclasses must implement delete")

    @property
    async def count(self) -> int:
        """Get the number of embeddings in the store."""
        raise NotImplementedError("Subclasses must implement count")


class PgVectorStore(VectorStore):
    """PostgreSQL vector store implementation using pgvector."""

    def __init__(
        self,
        dimension: int = None,
        table_name: str = "document_chunks",
        distance_metric: str = "cosine",
        **kwargs
    ):
        """
        Initialize the PostgreSQL vector store.

        Args:
            dimension: Dimension of the embedding vectors
            table_name: Name of the table to store embeddings
            distance_metric: Distance metric for similarity search
            **kwargs: Additional arguments
        """
        # Get dimension from settings if not provided
        if dimension is None:
            self.dimension = get_settings().VECTOR_DIMENSION
        else:
            self.dimension = dimension

        self.table_name = table_name
        self.distance_metric = distance_metric

        # Map distance metric to SQL function
        self.distance_func = {
            "cosine": "1 - (embedding <=> :query_embedding)",
            "l2": "-1 * (embedding <-> :query_embedding)",
            "ip": "embedding <#> :query_embedding"
        }.get(distance_metric, "1 - (embedding <=> :query_embedding)")

        logger.info(f"Initialized PgVectorStore with dimension {self.dimension}")

    async def add_embeddings(
        self,
        embeddings: List[List[float]],
        texts: List[str],
        metadatas: Optional[List[Dict[str, Any]]] = None,
        ids: Optional[List[str]] = None
    ) -> List[str]:
        """
        Add embeddings to the vector store.

        Args:
            embeddings: List of embedding vectors
            texts: List of text strings corresponding to the embeddings
            metadatas: Optional list of metadata dictionaries
            ids: Optional list of IDs for the embeddings

        Returns:
            List of IDs for the added embeddings
        """
        # Validate inputs
        if len(embeddings) != len(texts):
            raise ValueError("Number of embeddings must match number of texts")

        # Generate IDs if not provided
        if ids is None:
            ids = [str(uuid.uuid4()) for _ in range(len(embeddings))]
        elif len(ids) != len(embeddings):
            raise ValueError("Number of IDs must match number of embeddings")

        # Use empty metadata if not provided
        if metadatas is None:
            metadatas = [{} for _ in range(len(embeddings))]
        elif len(metadatas) != len(embeddings):
            raise ValueError("Number of metadata items must match number of embeddings")

        # Add embeddings to the database
        with get_db_context() as db:
            try:
                # Insert embeddings into the database
                for i, (doc_id, embedding, text, metadata) in enumerate(zip(ids, embeddings, texts, metadatas)):
                    # Convert embedding to PostgreSQL vector format
                    vector_str = f"[{','.join(str(x) for x in embedding)}]"

                    # Insert the embedding
                    db.execute(
                        text(f"""
                            INSERT INTO {self.table_name} (id, content, metadata, embedding)
                            VALUES (:id, :content, :metadata, :embedding::vector)
                            ON CONFLICT (id) DO UPDATE
                            SET content = :content, metadata = :metadata, embedding = :embedding::vector
                        """),
                        {
                            "id": doc_id,
                            "content": text,
                            "metadata": json.dumps(metadata),
                            "embedding": vector_str
                        }
                    )

                db.commit()
                logger.info(f"Added {len(embeddings)} embeddings to {self.table_name}")
                return ids

            except Exception as e:
                db.rollback()
                logger.error(f"Error adding embeddings to {self.table_name}: {e}")
                raise

    async def search(
        self,
        query_embedding: List[float],
        limit: int = 5,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        Search for similar embeddings.

        Args:
            query_embedding: The query embedding vector
            limit: Maximum number of results to return
            filters: Optional metadata filters

        Returns:
            List of dictionaries containing:
            - id: The embedding ID
            - text: The text string
            - metadata: The metadata dictionary
            - score: The similarity score
        """
        # Convert query embedding to PostgreSQL vector format
        vector_str = f"[{','.join(str(x) for x in query_embedding)}]"

        # Build the query
        query = f"""
            SELECT
                id,
                content,
                metadata,
                {self.distance_func} AS score
            FROM {self.table_name}
            WHERE embedding IS NOT NULL
        """

        # Add filters if provided
        filter_conditions = []
        params = {"query_embedding": vector_str}

        if filters:
            for key, value in filters.items():
                if isinstance(value, dict):
                    # Handle complex filters like {"$eq": "value"} or {"$in": ["val1", "val2"]}
                    if "$eq" in value:
                        filter_conditions.append(f"metadata->>{key!r} = :{key}")
                        params[key] = value["$eq"]
                    elif "$in" in value:
                        placeholders = ",".join(f":{key}_{i}" for i in range(len(value["$in"])))
                        filter_conditions.append(f"metadata->>{key!r} IN ({placeholders})")
                        for i, val in enumerate(value["$in"]):
                            params[f"{key}_{i}"] = val
                else:
                    # Simple equality filter
                    filter_conditions.append(f"metadata->>{key!r} = :{key}")
                    params[key] = value

        # Add filter conditions to query
        if filter_conditions:
            query += " AND " + " AND ".join(filter_conditions)

        # Add ordering and limit
        query += f" ORDER BY score DESC LIMIT {limit}"

        # Execute the query
        with get_db_context() as db:
            try:
                result = db.execute(text(query), params)
                rows = result.fetchall()

                # Convert results to the expected format
                results = []
                for row in rows:
                    # Parse metadata JSON
                    metadata = json.loads(row.metadata) if row.metadata else {}

                    results.append({
                        "id": row.id,
                        "text": row.content,
                        "metadata": metadata,
                        "score": float(row.score)
                    })

                logger.info(f"Vector search returned {len(results)} results")
                return results

            except Exception as e:
                logger.error(f"Error during vector search: {e}")
                raise

    async def delete(self, ids: List[str]) -> None:
        """
        Delete embeddings by ID.

        Args:
            ids: List of embedding IDs to delete
        """
        if not ids:
            return

        with get_db_context() as db:
            try:
                # Create placeholders for the IDs
                placeholders = ",".join(f":id_{i}" for i in range(len(ids)))
                params = {f"id_{i}": doc_id for i, doc_id in enumerate(ids)}

                # Delete the embeddings
                db.execute(
                    text(f"DELETE FROM {self.table_name} WHERE id IN ({placeholders})"),
                    params
                )

                db.commit()
                logger.info(f"Deleted {len(ids)} embeddings from {self.table_name}")

            except Exception as e:
                db.rollback()
                logger.error(f"Error deleting embeddings from {self.table_name}: {e}")
                raise

    @property
    async def count(self) -> int:
        """Get the number of embeddings in the store."""
        with get_db_context() as db:
            try:
                result = db.execute(
                    text(f"SELECT COUNT(*) FROM {self.table_name} WHERE embedding IS NOT NULL")
                )
                count = result.scalar()
                return count or 0

            except Exception as e:
                logger.error(f"Error counting embeddings in {self.table_name}: {e}")
                return 0


# Factory functions for creating vector stores
async def get_vector_store(
    store_type: str = "pgvector",
    dimension: int = None,
    **kwargs
) -> VectorStore:
    """
    Get a vector store instance.

    Args:
        store_type: Type of vector store (currently only "pgvector" supported)
        dimension: Dimension of the embedding vectors
        **kwargs: Additional arguments for the vector store

    Returns:
        Vector store instance
    """
    if store_type.lower() == "pgvector":
        return PgVectorStore(dimension=dimension, **kwargs)
    else:
        raise ValueError(f"Unsupported vector store type: {store_type}")


# Convenience aliases
FAISSVectorStore = PgVectorStore  # For compatibility
ChromaVectorStore = PgVectorStore  # For compatibility