"""
RAG Package

This package contains the implementation of the RAG (Retrieval-Augmented Generation) system:
- Embeddings: Convert text to vector representations
- Vector Store: Store and retrieve vectors efficiently
- Retriever: Process queries and retrieve relevant documents
- Generator: Combine retrieved context with LLM generation
- Knowledge Base: Service for accessing the knowledge base
- Query Analyzer: Analyze queries for intent and relevant departments
- Timeout: Timeout and retry utilities for RAG operations
"""

# Import core components
from .embeddings import EmbeddingModel, HuggingFaceEmbedding, OpenAIEmbedding, EmbeddingFactory, get_embedding_model
from .vector_store import VectorStore, PgVectorStore, get_vector_store
from .retriever import Retriever, HybridRetriever, ContextWindowManager, QueryRewriter
from .knowledge_base import KnowledgeBaseService
from .utils import RAGConfig, initialize_embedding_model, initialize_knowledge_base_service, EnhancedMockEmbeddingModel

__all__ = [
    # Core RAG components
    "EmbeddingModel",
    "HuggingFaceEmbedding",
    "OpenAIEmbedding",
    "EmbeddingFactory",
    "get_embedding_model",
    "VectorStore",
    "PgVectorStore",
    "get_vector_store",
    "Retriever",
    "HybridRetriever",
    "ContextWindowManager",
    "QueryRewriter",
    "KnowledgeBaseService",

    # Utility functions
    "RAGConfig",
    "initialize_embedding_model",
    "initialize_knowledge_base_service",
    "EnhancedMockEmbeddingModel"
]
