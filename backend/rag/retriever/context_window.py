"""
Context Window Management Utility

This module provides utilities for managing context window size for LLM input.
It can be used by agents right before they send prompts to LLMs.
"""

import logging
from typing import Dict, List, Any, Optional, Union

# Import optional dependencies
try:
    import tiktoken
    HAS_TIKTOKEN = True
except ImportError:
    HAS_TIKTOKEN = False

from app.core.timeout import with_timeout_and_retry

# Set up logging
logger = logging.getLogger(__name__)


class ContextWindowManager:
    """
    Manages context window size for LLM input.

    This class ensures that retrieved documents fit within the LLM's
    context window by truncating or summarizing as needed.
    """

    def __init__(
        self,
        llm_adapter,
        max_tokens: int = 4000,
        token_buffer: int = 1000
    ):
        """
        Initialize the context window manager.

        Args:
            llm_adapter: The LLM adapter to use for token counting and summarization
            max_tokens: Maximum tokens allowed in the context window
            token_buffer: Buffer tokens to reserve for the query and response
        """
        self.llm_adapter = llm_adapter
        self.max_tokens = max_tokens
        self.token_buffer = token_buffer

        # Import tiktoken for token counting if available
        self.has_tiktoken = HAS_TIKTOKEN
        if self.has_tiktoken:
            self.tokenizer = tiktoken.get_encoding("cl100k_base")

    async def fit_to_context_window(
        self,
        query: str,
        documents: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        Ensure documents fit within the context window.

        Args:
            query: The query text
            documents: The retrieved documents

        Returns:
            Documents that fit within the context window
        """
        # Estimate query tokens
        query_tokens = await self._count_tokens(query)

        # Calculate available tokens for documents
        available_tokens = self.max_tokens - query_tokens - self.token_buffer

        # Estimate tokens for each document
        documents_with_tokens = []
        for doc in documents:
            doc_tokens = await self._count_tokens(doc.get("text", ""))
            documents_with_tokens.append({
                **doc,
                "tokens": doc_tokens
            })

        # Sort by score (descending)
        documents_with_tokens.sort(key=lambda x: x.get("score", 0), reverse=True)

        # Fit documents to context window
        fitted_documents = []
        total_tokens = 0

        for doc in documents_with_tokens:
            doc_tokens = doc["tokens"]

            if total_tokens + doc_tokens <= available_tokens:
                # Document fits entirely
                fitted_doc = {k: v for k, v in doc.items() if k != "tokens"}
                fitted_documents.append(fitted_doc)
                total_tokens += doc_tokens
            elif total_tokens < available_tokens:
                # Document needs truncation
                truncated_text = await self._truncate_text(
                    doc.get("text", ""),
                    available_tokens - total_tokens
                )

                fitted_doc = {k: v for k, v in doc.items() if k != "tokens"}
                fitted_doc["text"] = truncated_text
                fitted_doc["truncated"] = True
                fitted_documents.append(fitted_doc)

                # Update total tokens
                truncated_tokens = await self._count_tokens(truncated_text)
                total_tokens += truncated_tokens
            else:
                # No more space available
                break

        return fitted_documents

    async def fit_prompt_to_context_window(
        self,
        messages: List[Dict[str, str]],
        max_tokens: Optional[int] = None
    ) -> List[Dict[str, str]]:
        """
        Ensure a prompt fits within the context window.
        
        This method is specifically designed for fitting chat messages
        before sending them to an LLM.

        Args:
            messages: List of message dictionaries with 'role' and 'content'
            max_tokens: Optional override for max tokens

        Returns:
            Fitted messages that will fit in the context window
        """
        if max_tokens is None:
            max_tokens = self.max_tokens
            
        # Reserve tokens for the response
        available_tokens = max_tokens - self.token_buffer
        
        # Count tokens in all messages
        total_tokens = 0
        messages_with_tokens = []
        
        for msg in messages:
            msg_tokens = await self._count_tokens(msg.get("content", ""))
            # Add overhead for message format (role, etc.)
            msg_tokens += 4  # Simple approximation for message overhead
            
            messages_with_tokens.append({
                **msg,
                "tokens": msg_tokens
            })
            total_tokens += msg_tokens
            
        # If everything fits, return original messages
        if total_tokens <= available_tokens:
            return messages
            
        # Otherwise, we need to truncate
        fitted_messages = []
        current_tokens = 0
        
        # Always keep the system message if present
        system_messages = [m for m in messages_with_tokens if m.get("role") == "system"]
        for msg in system_messages:
            fitted_messages.append({
                "role": msg["role"],
                "content": msg["content"]
            })
            current_tokens += msg["tokens"]
            
        # Always keep the most recent user message
        user_messages = [m for m in messages_with_tokens if m.get("role") == "user"]
        if user_messages:
            latest_user_msg = user_messages[-1]
            
            # Check if we need to truncate the user message
            if current_tokens + latest_user_msg["tokens"] > available_tokens:
                # Truncate the user message
                truncated_content = await self._truncate_text(
                    latest_user_msg["content"],
                    available_tokens - current_tokens
                )
                fitted_messages.append({
                    "role": "user",
                    "content": truncated_content
                })
            else:
                fitted_messages.append({
                    "role": "user",
                    "content": latest_user_msg["content"]
                })
                current_tokens += latest_user_msg["tokens"]
                
        # Add as many previous messages as possible, starting from the most recent
        remaining_messages = [
            m for m in messages_with_tokens 
            if m.get("role") != "system" and 
               (not user_messages or m != user_messages[-1])
        ]
        
        # Reverse to process from most recent to oldest
        remaining_messages.reverse()
        
        for msg in remaining_messages:
            if current_tokens + msg["tokens"] <= available_tokens:
                fitted_messages.insert(
                    len([m for m in fitted_messages if m.get("role") == "system"]), 
                    {
                        "role": msg["role"],
                        "content": msg["content"]
                    }
                )
                current_tokens += msg["tokens"]
            else:
                # No more space
                break
                
        return fitted_messages

    async def _count_tokens(self, text: str) -> int:
        """
        Count the number of tokens in a text.

        Args:
            text: The text to count tokens for

        Returns:
            Number of tokens
        """
        if self.has_tiktoken:
            # Use tiktoken for accurate token counting
            return len(self.tokenizer.encode(text))
        else:
            # Fall back to LLM adapter for token counting with timeout
            try:
                tokens_result = await with_timeout_and_retry(
                    self.llm_adapter.get_token_count,
                    [{"role": "user", "content": text}],
                    timeout_seconds=2,  # 2 seconds timeout for token counting
                    operation_name="llm_adapter.get_token_count",
                    max_attempts=1      # Only try once, fall back to character estimation
                )
                tokens, _ = tokens_result
                return tokens
            except Exception as e:
                logger.warning(f"Token counting with LLM adapter failed: {e}. Using character estimation.")
                # Last resort: estimate based on characters
                return len(text) // 4

    async def _truncate_text(self, text: str, max_tokens: int) -> str:
        """
        Truncate text to fit within max_tokens.

        Args:
            text: The text to truncate
            max_tokens: Maximum tokens allowed

        Returns:
            Truncated text
        """
        if self.has_tiktoken:
            # Use tiktoken for accurate truncation
            tokens = self.tokenizer.encode(text)
            if len(tokens) <= max_tokens:
                return text

            truncated_tokens = tokens[:max_tokens]
            return self.tokenizer.decode(truncated_tokens)
        else:
            # Estimate based on characters
            chars_per_token = 4
            max_chars = max_tokens * chars_per_token

            if len(text) <= max_chars:
                return text

            return text[:max_chars] + "..."


async def get_context_window_manager(
    llm_adapter,
    max_tokens: int = 4000,
    token_buffer: int = 1000
) -> ContextWindowManager:
    """
    Get a context window manager instance.
    
    Args:
        llm_adapter: The LLM adapter to use for token counting and summarization
        max_tokens: Maximum tokens allowed in the context window
        token_buffer: Buffer tokens to reserve for the query and response
        
    Returns:
        ContextWindowManager instance
    """
    return ContextWindowManager(
        llm_adapter=llm_adapter,
        max_tokens=max_tokens,
        token_buffer=token_buffer
    )