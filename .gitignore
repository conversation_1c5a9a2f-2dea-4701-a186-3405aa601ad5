# Python
__pycache__/
*.py[cod]
*.pyo
*.pyd

# Virtual environments
.venv/
.uv/
venv/

# Environment files
.env
.env.*
.env.local
.env.development
.env.test
.env.production

# Logs
*.log
logs/
backend/server.log

# Local database and data files
*.db
backend/data/checkpoints.db

# Test cache
.pytest_cache/

# Editor & IDE configs
.vscode/
!.vscode/extensions.json
.idea/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Security-sensitive files
*.pem
*.key
*.cert
*.crt
*service-account.json
*-credentials.json
*-keyfile.json

